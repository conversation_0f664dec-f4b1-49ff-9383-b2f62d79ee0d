<template>
  <div class="prompt-customizer">
    <el-card class="customizer-card">
      <template #header>
        <div class="card-header">
          <h3>自訂 AI Prompt 設定</h3>
          <el-switch
            v-model="forceJsonFormat"
            active-text="強制 JSON 格式"
            inactive-text="允許原始回覆"
            @change="onFormatModeChange"
          />
        </div>
      </template>

      <div class="prompt-tabs">
        <el-tabs v-model="activeTab" type="card">
          <el-tab-pane
            v-for="template in promptTemplates"
            :key="template.step_name"
            :label="getStepDisplayName(template.step_name)"
            :name="template.step_name"
          >
            <div class="prompt-editor">
              <div class="template-info">
                <el-alert
                  :title="template.description"
                  type="info"
                  :closable="false"
                  show-icon
                />
                <div class="variables-info">
                  <span class="variables-label">可用變數：</span>
                  <el-tag
                    v-for="variable in template.variables"
                    :key="variable"
                    size="small"
                    type="primary"
                  >
                    {{ `{${variable}}` }}
                  </el-tag>
                </div>
              </div>

              <div class="editor-section">
                <div class="editor-header">
                  <span class="editor-title">自訂 Prompt</span>
                  <div class="editor-actions">
                    <el-button
                      size="small"
                      @click="resetToDefault(template.step_name)"
                    >
                      重置為預設
                    </el-button>
                    <el-button
                      size="small"
                      type="primary"
                      @click="validatePrompt(template.step_name)"
                    >
                      驗證格式
                    </el-button>
                  </div>
                </div>

                <el-input
                  v-model="customPrompts[template.step_name]"
                  type="textarea"
                  :rows="12"
                  placeholder="輸入您的自訂 prompt..."
                  class="prompt-textarea"
                />

                <div v-if="validationResults[template.step_name]" class="validation-result">
                  <el-alert
                    :title="validationResults[template.step_name].valid ? '格式驗證通過' : '格式驗證失敗'"
                    :type="validationResults[template.step_name].valid ? 'success' : 'warning'"
                    :description="validationResults[template.step_name].warning"
                    :closable="false"
                    show-icon
                  />
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <div class="action-buttons">
        <el-button @click="loadTaskPrompts" :loading="loading">
          載入任務設定
        </el-button>
        <el-button type="primary" @click="savePrompts" :loading="saving">
          儲存設定
        </el-button>
        <el-button type="danger" @click="clearAllPrompts">
          清除所有自訂
        </el-button>
      </div>
    </el-card>

    <!-- 預覽對話框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="Prompt 預覽"
      width="80%"
      :before-close="closePreviewDialog"
    >
      <div class="preview-content">
        <h4>{{ getStepDisplayName(previewStep) }}</h4>
        <pre class="prompt-preview">{{ previewContent }}</pre>
      </div>
      <template #footer>
        <el-button @click="closePreviewDialog">關閉</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { api } from '@/utils/api'

interface PromptTemplate {
  step_name: string
  default_prompt: string
  description: string
  variables: string[]
}

interface ValidationResult {
  valid: boolean
  step_name: string
  required_variables: string[]
  missing_variables: string[]
  prompt_length: number
  warning?: string
}

// Props
const props = defineProps<{
  taskId?: string
}>()

// 響應式數據
const activeTab = ref('clauses_analysis')
const forceJsonFormat = ref(true)
const loading = ref(false)
const saving = ref(false)
const previewDialogVisible = ref(false)
const previewStep = ref('')
const previewContent = ref('')

const promptTemplates = ref<PromptTemplate[]>([])
const customPrompts = reactive<Record<string, string>>({})
const validationResults = reactive<Record<string, ValidationResult>>({})

// 計算屬性
const hasCustomPrompts = computed(() => {
  return Object.values(customPrompts).some(prompt => prompt.trim() !== '')
})

// 方法
const getStepDisplayName = (stepName: string): string => {
  const displayNames: Record<string, string> = {
    'clauses_analysis': '條款分析',
    'compliance_check': '法規檢查',
    'final_report': '最終報告'
  }
  return displayNames[stepName] || stepName
}

const loadPromptTemplates = async () => {
  try {
    loading.value = true
    const response = await api.get('/prompt-management/templates')
    promptTemplates.value = response.data
    
    // 初始化自訂 prompts
    promptTemplates.value.forEach(template => {
      if (!customPrompts[template.step_name]) {
        customPrompts[template.step_name] = ''
      }
    })
  } catch (error) {
    console.error('載入 Prompt 模板失敗:', error)
    ElMessage.error('載入 Prompt 模板失敗')
  } finally {
    loading.value = false
  }
}

const loadTaskPrompts = async () => {
  if (!props.taskId) {
    ElMessage.warning('請先選擇一個任務')
    return
  }

  try {
    loading.value = true
    const response = await api.get(`/prompt-management/tasks/${props.taskId}/prompts`)
    const taskConfig = response.data
    
    // 載入任務的自訂 prompts
    Object.assign(customPrompts, taskConfig.custom_prompts || {})
    forceJsonFormat.value = taskConfig.force_json_format ?? true
    
    ElMessage.success('任務設定載入成功')
  } catch (error) {
    console.error('載入任務設定失敗:', error)
    ElMessage.error('載入任務設定失敗')
  } finally {
    loading.value = false
  }
}

const savePrompts = async () => {
  if (!props.taskId) {
    ElMessage.warning('請先選擇一個任務')
    return
  }

  try {
    saving.value = true
    
    // 過濾掉空的 prompts
    const filteredPrompts: Record<string, string> = {}
    Object.entries(customPrompts).forEach(([key, value]) => {
      if (value.trim()) {
        filteredPrompts[key] = value
      }
    })

    await api.post(`/prompt-management/tasks/${props.taskId}/configure`, {
      task_id: props.taskId,
      custom_prompts: filteredPrompts,
      force_json_format: forceJsonFormat.value
    })
    
    ElMessage.success('Prompt 設定儲存成功')
  } catch (error) {
    console.error('儲存 Prompt 設定失敗:', error)
    ElMessage.error('儲存 Prompt 設定失敗')
  } finally {
    saving.value = false
  }
}

const validatePrompt = async (stepName: string) => {
  const promptContent = customPrompts[stepName]
  if (!promptContent.trim()) {
    ElMessage.warning('請先輸入 Prompt 內容')
    return
  }

  try {
    const response = await api.post('/prompt-management/validate', {
      step_name: stepName,
      prompt_content: promptContent,
      description: `自訂 ${getStepDisplayName(stepName)} Prompt`
    })
    
    validationResults[stepName] = response.data
    
    if (response.data.valid) {
      ElMessage.success('Prompt 格式驗證通過')
    } else {
      ElMessage.warning(`Prompt 格式有問題: ${response.data.warning}`)
    }
  } catch (error) {
    console.error('驗證 Prompt 失敗:', error)
    ElMessage.error('驗證 Prompt 失敗')
  }
}

const resetToDefault = (stepName: string) => {
  const template = promptTemplates.value.find(t => t.step_name === stepName)
  if (template) {
    customPrompts[stepName] = template.default_prompt
    delete validationResults[stepName]
    ElMessage.success('已重置為預設 Prompt')
  }
}

const clearAllPrompts = async () => {
  try {
    await ElMessageBox.confirm(
      '確定要清除所有自訂 Prompt 嗎？此操作無法復原。',
      '確認清除',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    Object.keys(customPrompts).forEach(key => {
      customPrompts[key] = ''
    })
    Object.keys(validationResults).forEach(key => {
      delete validationResults[key]
    })
    
    ElMessage.success('已清除所有自訂 Prompt')
  } catch {
    // 用戶取消操作
  }
}

const onFormatModeChange = (value: boolean) => {
  const mode = value ? 'JSON 格式' : '原始回覆'
  ElMessage.info(`已切換到 ${mode} 模式`)
}

const closePreviewDialog = () => {
  previewDialogVisible.value = false
  previewStep.value = ''
  previewContent.value = ''
}

// 生命週期
onMounted(() => {
  loadPromptTemplates()
  if (props.taskId) {
    loadTaskPrompts()
  }
})
</script>

<style scoped>
.prompt-customizer {
  max-width: 1200px;
  margin: 0 auto;
}

.customizer-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.prompt-tabs {
  margin-bottom: 20px;
}

.template-info {
  margin-bottom: 20px;
}

.variables-info {
  margin-top: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.variables-label {
  font-weight: 500;
  color: #606266;
}

.editor-section {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.editor-title {
  font-weight: 500;
  color: #303133;
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.prompt-textarea {
  border: none;
}

.prompt-textarea :deep(.el-textarea__inner) {
  border: none;
  border-radius: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.validation-result {
  padding: 12px 16px;
  border-top: 1px solid #dcdfe6;
  background-color: #fafafa;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.preview-content h4 {
  margin-top: 0;
  color: #303133;
}

.prompt-preview {
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  white-space: pre-wrap;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  max-height: 400px;
  overflow-y: auto;
}
</style>
