<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PreviewItemTable 測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 15px;
        }
        .api-test {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 5px 0;
            font-weight: bold;
        }
        .status.success { background: #f0f9ff; color: #059669; }
        .status.error { background: #fef2f2; color: #dc2626; }
        .status.info { background: #f0f9ff; color: #2563eb; }
        .result {
            background: #f9fafb;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PreviewItemTable API 測試</h1>
        
        <div class="test-section">
            <div class="test-title">1. 測試任務管理API</div>
            <div class="api-test">
                <button onclick="testGetPurchaseTasks()">測試獲取購案任務</button>
                <button onclick="testGetTaskResult()">測試獲取任務結果</button>
                <div id="task-status" class="status info">準備測試...</div>
                <div id="task-result" class="result"></div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 測試分析結果API</div>
            <div class="api-test">
                <button onclick="testGetAnalysisResults()">測試獲取分析結果列表</button>
                <button onclick="testGetAnalysisResult()">測試獲取單個分析結果</button>
                <div id="result-status" class="status info">準備測試...</div>
                <div id="result-result" class="result"></div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 模擬任務數據</div>
            <div class="api-test">
                <button onclick="showMockData()">顯示模擬任務數據</button>
                <div id="mock-status" class="status info">準備顯示...</div>
                <div id="mock-result" class="result"></div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001/api/v1';
        const TEST_PURCHASE_ID = '1'; // 測試用購案ID
        const TEST_TASK_ID = 'test-task-1'; // 測試用任務ID

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        function showResult(elementId, data) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
        }

        async function testGetPurchaseTasks() {
            showStatus('task-status', '正在測試獲取購案任務...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/task-management/purchase/${TEST_PURCHASE_ID}/tasks`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                showStatus('task-status', `成功獲取 ${data.task_count || 0} 個任務`, 'success');
                showResult('task-result', data);
                
            } catch (error) {
                showStatus('task-status', `測試失敗: ${error.message}`, 'error');
                showResult('task-result', { error: error.message });
            }
        }

        async function testGetTaskResult() {
            showStatus('task-status', '正在測試獲取任務結果...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/analysis-results/tasks/${TEST_TASK_ID}/result`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                showStatus('task-status', '成功獲取任務結果', 'success');
                showResult('task-result', data);
                
            } catch (error) {
                showStatus('task-status', `測試失敗: ${error.message}`, 'error');
                showResult('task-result', { error: error.message });
            }
        }

        async function testGetAnalysisResults() {
            showStatus('result-status', '正在測試獲取分析結果列表...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/analysis-results/results?purchase_id=${TEST_PURCHASE_ID}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                showStatus('result-status', `成功獲取 ${data.total_count || 0} 個結果`, 'success');
                showResult('result-result', data);
                
            } catch (error) {
                showStatus('result-status', `測試失敗: ${error.message}`, 'error');
                showResult('result-result', { error: error.message });
            }
        }

        async function testGetAnalysisResult() {
            showStatus('result-status', '正在測試獲取單個分析結果...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/analysis-results/results/test-result-1?include_content=true`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                showStatus('result-status', '成功獲取分析結果詳情', 'success');
                showResult('result-result', data);
                
            } catch (error) {
                showStatus('result-status', `測試失敗: ${error.message}`, 'error');
                showResult('result-result', { error: error.message });
            }
        }

        function showMockData() {
            showStatus('mock-status', '顯示模擬任務數據', 'success');
            
            const mockTasks = [
                {
                    task_id: "task-1",
                    task_name: "法規比對",
                    description: "與中心相關作業規定比對",
                    status: "completed",
                    progress: 100,
                    result_data: { result_id: "result-1" }
                },
                {
                    task_id: "task-2", 
                    task_name: "陸製品限制比對",
                    description: "與中心相關作業規定比對",
                    status: "running",
                    progress: 60,
                    result_data: null
                },
                {
                    task_id: "task-3",
                    task_name: "需求合理性(含籌補率)",
                    description: "生產用料以料件籌補分析表比對審查",
                    status: "pending",
                    progress: 0,
                    result_data: null
                }
            ];
            
            showResult('mock-result', {
                purchase_id: TEST_PURCHASE_ID,
                task_count: mockTasks.length,
                tasks: mockTasks
            });
        }

        // 頁面載入時顯示說明
        window.onload = function() {
            console.log('PreviewItemTable API 測試頁面已載入');
            console.log('API Base URL:', API_BASE);
            console.log('測試購案ID:', TEST_PURCHASE_ID);
        };
    </script>
</body>
</html>
