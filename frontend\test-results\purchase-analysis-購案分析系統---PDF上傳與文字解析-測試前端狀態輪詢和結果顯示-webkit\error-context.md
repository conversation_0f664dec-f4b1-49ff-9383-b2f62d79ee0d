# Page snapshot

```yaml
- img
- heading "購案審查系統" [level=1]
- menubar:
  - menuitem "首頁":
    - img
    - text: 首頁
  - menuitem "購案分析":
    - img
    - text: 購案分析
  - menuitem "購案管理":
    - img
    - text: 購案管理
  - menuitem "組件演示":
    - img
    - text: 組件演示
  - menuitem "知識庫":
    - img
    - text: 知識庫
  - menuitem "GraphRAG 訓練":
    - img
    - text: GraphRAG 訓練
  - menuitem "任務管理":
    - img
    - text: 任務管理
  - menuitem:
    - img
- button "登入"
- main
- paragraph: © 2024 購案審查系統. All rights reserved.
- paragraph: Powered by Vue.js + ElementVuePlus + GraphRAG
- img
- img
- text: "[plugin:vite:vue] v-model cannot be used on a prop, because local prop bindings are not writable. Use a v-bind binding combined with a v-on listener that emits update:x event instead. C:/home/<USER>/repo/frontend/src/components/TaskResultViewer.vue:3:14 1 | <template> 2 | <el-dialog 3 | v-model=\"visible\" | ^ 4 | :title=\"dialogTitle\" 5 | width=\"90vw\" at createCompilerError (C:\\home\\PurchaseReview\\repo\\frontend\\node_modules\\@vue\\compiler-core\\dist\\compiler-core.cjs.js:1364:17) at Object.transformModel (C:\\home\\PurchaseReview\\repo\\frontend\\node_modules\\@vue\\compiler-core\\dist\\compiler-core.cjs.js:6363:21) at transformModel (C:\\home\\PurchaseReview\\repo\\frontend\\node_modules\\@vue\\compiler-dom\\dist\\compiler-dom.cjs.js:219:35) at buildProps (C:\\home\\PurchaseReview\\repo\\frontend\\node_modules\\@vue\\compiler-core\\dist\\compiler-core.cjs.js:5854:48) at Array.postTransformElement (C:\\home\\PurchaseReview\\repo\\frontend\\node_modules\\@vue\\compiler-core\\dist\\compiler-core.cjs.js:5476:32) at traverseNode (C:\\home\\PurchaseReview\\repo\\frontend\\node_modules\\@vue\\compiler-core\\dist\\compiler-core.cjs.js:3591:15) at traverseChildren (C:\\home\\PurchaseReview\\repo\\frontend\\node_modules\\@vue\\compiler-core\\dist\\compiler-core.cjs.js:3542:5) at traverseNode (C:\\home\\PurchaseReview\\repo\\frontend\\node_modules\\@vue\\compiler-core\\dist\\compiler-core.cjs.js:3585:7) at transform (C:\\home\\PurchaseReview\\repo\\frontend\\node_modules\\@vue\\compiler-core\\dist\\compiler-core.cjs.js:3478:3) at Object.baseCompile (C:\\home\\PurchaseReview\\repo\\frontend\\node_modules\\@vue\\compiler-core\\dist\\compiler-core.cjs.js:6656:3 Click outside, press Esc key, or fix the code to dismiss. You can also disable this overlay by setting"
- code: server.hmr.overlay
- text: to
- code: "false"
- text: in
- code: vite.config.ts.
```