"""
分析結果儲存服務
"""

import os
import json
import uuid
import shutil
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
import logging

from app.models.analysis_result import AnalysisResult, ResultType, ResultStatus, ConfidenceLevel
from app.models.purchase import Purchase
from app.models.analysis_task import AnalysisTask
from app.core.database import get_db
from app.core.config import settings

logger = logging.getLogger(__name__)


class ResultStorageService:
    """分析結果儲存服務類"""

    def __init__(self, db: Session):
        self.db = db
        self.base_path = Path(settings.RESULTS_DIR) if hasattr(settings, 'RESULTS_DIR') else Path("./analysis_results")
        self.base_path.mkdir(parents=True, exist_ok=True)

    async def store_analysis_result(
        self,
        purchase_id: str,
        task_id: Optional[str] = None,
        title: str = "",
        description: Optional[str] = None,
        result_type: ResultType = ResultType.ANALYSIS,
        content_data: Optional[Dict[str, Any]] = None,
        analysis_metadata: Optional[Dict[str, Any]] = None,
        files: Optional[List[Dict[str, Any]]] = None
    ) -> AnalysisResult:
        """儲存分析結果"""
        
        logger.info(f"開始儲存分析結果: 購案 {purchase_id}")
        
        try:
            # 生成結果ID
            result_id = str(uuid.uuid4())
            
            # 創建結果目錄
            result_dir = await self._create_result_directory(purchase_id, result_id)
            
            # 處理內容數據
            processed_content = await self._process_content_data(content_data or {})
            
            # 處理文件
            output_files = await self._process_result_files(result_dir, files or [])
            
            # 計算統計信息
            statistics = await self._calculate_statistics(processed_content)
            
            # 創建分析結果記錄
            analysis_result = AnalysisResult(
                result_id=result_id,
                purchase_id=purchase_id,
                task_id=task_id,
                title=title or f"分析結果 - {datetime.utcnow().strftime('%Y-%m-%d %H:%M')}",
                description=description,
                result_type=result_type,
                status=ResultStatus.COMPLETED,
                
                # 內容數據
                summary=processed_content.get('summary'),
                content=processed_content.get('content'),
                key_findings=processed_content.get('key_findings'),
                recommendations=processed_content.get('recommendations'),
                
                # 評分信息
                confidence_score=processed_content.get('confidence_score'),
                quality_score=processed_content.get('quality_score'),
                relevance_score=processed_content.get('relevance_score'),
                
                # 實體和關係
                entities=processed_content.get('entities'),
                relationships=processed_content.get('relationships'),
                keywords=processed_content.get('keywords'),
                topics=processed_content.get('topics'),
                
                # 統計信息
                word_count=statistics.get('word_count', 0),
                sentence_count=statistics.get('sentence_count', 0),
                paragraph_count=statistics.get('paragraph_count', 0),
                page_count=statistics.get('page_count', 0),
                
                # 分析元數據
                analysis_model=analysis_metadata.get('model') if analysis_metadata else None,
                analysis_method=analysis_metadata.get('method') if analysis_metadata else None,
                model_version=analysis_metadata.get('version') if analysis_metadata else None,
                parameters=analysis_metadata.get('parameters') if analysis_metadata else None,
                
                # 文件信息
                source_files=processed_content.get('source_files'),
                output_files=output_files,
                
                # 時間信息
                analysis_start_time=analysis_metadata.get('start_time') if analysis_metadata else None,
                analysis_end_time=analysis_metadata.get('end_time') if analysis_metadata else None,
                analysis_duration=analysis_metadata.get('duration') if analysis_metadata else None
            )
            
            # 設置信心度等級
            if analysis_result.confidence_score:
                analysis_result.confidence_level = analysis_result._get_confidence_level(analysis_result.confidence_score)
            
            # 保存到數據庫
            self.db.add(analysis_result)
            self.db.commit()
            self.db.refresh(analysis_result)
            
            # 保存詳細結果到文件
            await self._save_result_files(result_dir, analysis_result, processed_content)
            
            logger.info(f"分析結果儲存完成: {result_id}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"儲存分析結果失敗: {e}")
            self.db.rollback()
            raise

    async def get_analysis_result(self, result_id: str) -> Optional[AnalysisResult]:
        """獲取分析結果"""
        
        return self.db.query(AnalysisResult).filter(
            and_(
                AnalysisResult.result_id == result_id,
                AnalysisResult.is_deleted == False
            )
        ).first()

    async def get_purchase_results(
        self,
        purchase_id: str,
        result_type: Optional[ResultType] = None,
        status: Optional[ResultStatus] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[AnalysisResult]:
        """獲取購案的分析結果列表"""
        
        query = self.db.query(AnalysisResult).filter(
            and_(
                AnalysisResult.purchase_id == purchase_id,
                AnalysisResult.is_deleted == False
            )
        )
        
        if result_type:
            query = query.filter(AnalysisResult.result_type == result_type)
        
        if status:
            query = query.filter(AnalysisResult.status == status)
        
        return query.order_by(desc(AnalysisResult.created_time)).offset(skip).limit(limit).all()

    async def get_result_content(self, result_id: str) -> Optional[Dict[str, Any]]:
        """獲取結果的詳細內容"""
        
        result = await self.get_analysis_result(result_id)
        if not result:
            return None
        
        # 從文件系統讀取詳細內容
        result_dir = self.base_path / result.purchase_id / result_id
        content_file = result_dir / "content.json"
        
        if content_file.exists():
            try:
                with open(content_file, 'r', encoding='utf-8') as f:
                    file_content = json.load(f)
                
                # 合併數據庫和文件內容
                return {
                    **result.to_dict(),
                    'detailed_content': file_content
                }
            except Exception as e:
                logger.error(f"讀取結果內容文件失敗: {e}")
        
        return result.to_dict()

    async def update_analysis_result(
        self,
        result_id: str,
        updates: Dict[str, Any]
    ) -> Optional[AnalysisResult]:
        """更新分析結果"""
        
        result = await self.get_analysis_result(result_id)
        if not result:
            return None
        
        try:
            # 更新允許的字段
            updatable_fields = [
                'title', 'description', 'summary', 'content', 'key_findings',
                'recommendations', 'confidence_score', 'quality_score',
                'relevance_score', 'entities', 'relationships', 'keywords',
                'topics', 'tags', 'categories', 'priority', 'metadata'
            ]
            
            for field, value in updates.items():
                if field in updatable_fields and hasattr(result, field):
                    setattr(result, field, value)
            
            # 更新信心度等級
            if 'confidence_score' in updates and updates['confidence_score']:
                result.confidence_level = result._get_confidence_level(updates['confidence_score'])
            
            self.db.commit()
            self.db.refresh(result)
            
            # 更新文件內容
            if any(field in updates for field in ['content', 'key_findings', 'recommendations']):
                await self._update_result_files(result, updates)
            
            logger.info(f"分析結果更新完成: {result_id}")
            return result
            
        except Exception as e:
            logger.error(f"更新分析結果失敗: {e}")
            self.db.rollback()
            raise

    async def delete_analysis_result(self, result_id: str, hard_delete: bool = False) -> bool:
        """刪除分析結果"""
        
        result = await self.get_analysis_result(result_id)
        if not result:
            return False
        
        try:
            if hard_delete:
                # 硬刪除：刪除文件和數據庫記錄
                await self._delete_result_files(result)
                self.db.delete(result)
            else:
                # 軟刪除
                result.soft_delete()
            
            self.db.commit()
            
            logger.info(f"分析結果刪除完成: {result_id} (硬刪除: {hard_delete})")
            return True
            
        except Exception as e:
            logger.error(f"刪除分析結果失敗: {e}")
            self.db.rollback()
            raise

    async def export_result(
        self,
        result_id: str,
        export_format: str = "json",
        include_files: bool = True
    ) -> Optional[Dict[str, Any]]:
        """導出分析結果"""
        
        result = await self.get_analysis_result(result_id)
        if not result:
            return None
        
        try:
            # 獲取完整內容
            full_content = await self.get_result_content(result_id)
            
            if export_format.lower() == "json":
                export_data = {
                    'metadata': {
                        'export_time': datetime.utcnow().isoformat(),
                        'export_format': export_format,
                        'result_id': result_id
                    },
                    'result': full_content
                }
                
                if include_files:
                    # 包含文件信息
                    result_dir = self.base_path / result.purchase_id / result_id
                    files_info = await self._get_result_files_info(result_dir)
                    export_data['files'] = files_info
                
                return export_data
            
            # 其他格式可以在這裡擴展
            else:
                raise ValueError(f"不支持的導出格式: {export_format}")
                
        except Exception as e:
            logger.error(f"導出分析結果失敗: {e}")
            raise

    async def search_results(
        self,
        query: str,
        purchase_id: Optional[str] = None,
        result_type: Optional[ResultType] = None,
        limit: int = 50
    ) -> List[AnalysisResult]:
        """搜索分析結果"""
        
        search_query = self.db.query(AnalysisResult).filter(
            AnalysisResult.is_deleted == False
        )
        
        # 購案篩選
        if purchase_id:
            search_query = search_query.filter(AnalysisResult.purchase_id == purchase_id)
        
        # 類型篩選
        if result_type:
            search_query = search_query.filter(AnalysisResult.result_type == result_type)
        
        # 關鍵詞搜索
        search_pattern = f"%{query}%"
        search_query = search_query.filter(
            or_(
                AnalysisResult.title.like(search_pattern),
                AnalysisResult.description.like(search_pattern),
                AnalysisResult.summary.like(search_pattern),
                AnalysisResult.content.like(search_pattern)
            )
        )
        
        return search_query.order_by(desc(AnalysisResult.created_time)).limit(limit).all()

    async def get_result_statistics(self) -> Dict[str, Any]:
        """獲取結果統計信息"""
        
        total_count = self.db.query(AnalysisResult).filter(AnalysisResult.is_deleted == False).count()
        
        # 按類型統計
        type_counts = {}
        for result_type in ResultType:
            count = self.db.query(AnalysisResult).filter(
                and_(
                    AnalysisResult.result_type == result_type,
                    AnalysisResult.is_deleted == False
                )
            ).count()
            type_counts[result_type.value] = count
        
        # 按狀態統計
        status_counts = {}
        for status in ResultStatus:
            count = self.db.query(AnalysisResult).filter(
                and_(
                    AnalysisResult.status == status,
                    AnalysisResult.is_deleted == False
                )
            ).count()
            status_counts[status.value] = count
        
        # 按信心度等級統計
        confidence_counts = {}
        for level in ConfidenceLevel:
            count = self.db.query(AnalysisResult).filter(
                and_(
                    AnalysisResult.confidence_level == level,
                    AnalysisResult.is_deleted == False
                )
            ).count()
            confidence_counts[level.value] = count
        
        # 計算平均評分
        results = self.db.query(AnalysisResult).filter(AnalysisResult.is_deleted == False).all()
        
        avg_confidence = sum(r.confidence_score for r in results if r.confidence_score) / len([r for r in results if r.confidence_score]) if results else 0
        avg_quality = sum(r.quality_score for r in results if r.quality_score) / len([r for r in results if r.quality_score]) if results else 0
        avg_relevance = sum(r.relevance_score for r in results if r.relevance_score) / len([r for r in results if r.relevance_score]) if results else 0
        
        return {
            'total_count': total_count,
            'type_counts': type_counts,
            'status_counts': status_counts,
            'confidence_counts': confidence_counts,
            'average_scores': {
                'confidence': round(avg_confidence, 2),
                'quality': round(avg_quality, 2),
                'relevance': round(avg_relevance, 2)
            },
            'total_storage_size': await self._calculate_total_storage_size()
        }

    async def _create_result_directory(self, purchase_id: str, result_id: str) -> Path:
        """創建結果目錄"""
        
        result_dir = self.base_path / purchase_id / result_id
        result_dir.mkdir(parents=True, exist_ok=True)
        
        # 創建子目錄
        (result_dir / "files").mkdir(exist_ok=True)
        (result_dir / "attachments").mkdir(exist_ok=True)
        (result_dir / "exports").mkdir(exist_ok=True)
        
        return result_dir

    async def _process_content_data(self, content_data: Dict[str, Any]) -> Dict[str, Any]:
        """處理內容數據"""
        
        processed = {}
        
        # 基本內容
        processed['summary'] = content_data.get('summary', '')
        processed['content'] = content_data.get('content', '')
        
        # 結構化數據
        processed['key_findings'] = content_data.get('key_findings', [])
        processed['recommendations'] = content_data.get('recommendations', [])
        processed['entities'] = content_data.get('entities', [])
        processed['relationships'] = content_data.get('relationships', [])
        processed['keywords'] = content_data.get('keywords', [])
        processed['topics'] = content_data.get('topics', [])
        
        # 評分
        processed['confidence_score'] = content_data.get('confidence_score')
        processed['quality_score'] = content_data.get('quality_score')
        processed['relevance_score'] = content_data.get('relevance_score')
        
        # 源文件信息
        processed['source_files'] = content_data.get('source_files', [])
        
        return processed

    async def _process_result_files(self, result_dir: Path, files: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """處理結果文件"""
        
        output_files = []
        files_dir = result_dir / "files"
        
        for file_info in files:
            try:
                file_name = file_info.get('name', f"file_{len(output_files)}")
                file_content = file_info.get('content')
                file_type = file_info.get('type', 'text')
                
                if file_content:
                    file_path = files_dir / file_name
                    
                    if file_type == 'json':
                        with open(file_path, 'w', encoding='utf-8') as f:
                            json.dump(file_content, f, ensure_ascii=False, indent=2)
                    elif file_type == 'text':
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(str(file_content))
                    else:
                        # 二進制文件
                        with open(file_path, 'wb') as f:
                            f.write(file_content)
                    
                    output_files.append({
                        'name': file_name,
                        'path': str(file_path),
                        'type': file_type,
                        'size': file_path.stat().st_size,
                        'created_time': datetime.utcnow().isoformat()
                    })
                    
            except Exception as e:
                logger.error(f"處理結果文件失敗: {e}")
        
        return output_files

    async def _calculate_statistics(self, content_data: Dict[str, Any]) -> Dict[str, int]:
        """計算統計信息"""
        
        content = content_data.get('content', '')
        summary = content_data.get('summary', '')
        
        full_text = f"{summary} {content}"
        
        # 簡單的統計計算
        word_count = len(full_text.split()) if full_text else 0
        sentence_count = full_text.count('.') + full_text.count('!') + full_text.count('?') if full_text else 0
        paragraph_count = full_text.count('\n\n') + 1 if full_text else 0
        
        return {
            'word_count': word_count,
            'sentence_count': sentence_count,
            'paragraph_count': paragraph_count,
            'page_count': content_data.get('page_count', 0)
        }

    async def _save_result_files(
        self,
        result_dir: Path,
        analysis_result: AnalysisResult,
        content_data: Dict[str, Any]
    ):
        """保存結果文件"""
        
        # 保存完整內容
        content_file = result_dir / "content.json"
        with open(content_file, 'w', encoding='utf-8') as f:
            json.dump({
                'basic_info': analysis_result.to_dict(),
                'detailed_content': content_data,
                'saved_at': datetime.utcnow().isoformat()
            }, f, ensure_ascii=False, indent=2)
        
        # 保存摘要
        if analysis_result.summary:
            summary_file = result_dir / "summary.txt"
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write(analysis_result.summary)
        
        # 保存關鍵發現
        if analysis_result.key_findings:
            findings_file = result_dir / "key_findings.json"
            with open(findings_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_result.key_findings, f, ensure_ascii=False, indent=2)

    async def _update_result_files(self, result: AnalysisResult, updates: Dict[str, Any]):
        """更新結果文件"""
        
        result_dir = self.base_path / result.purchase_id / result.result_id
        
        if 'content' in updates or 'summary' in updates:
            # 重新保存內容文件
            await self._save_result_files(result_dir, result, updates)

    async def _delete_result_files(self, result: AnalysisResult):
        """刪除結果文件"""
        
        result_dir = self.base_path / result.purchase_id / result.result_id
        if result_dir.exists():
            shutil.rmtree(result_dir)

    async def _get_result_files_info(self, result_dir: Path) -> List[Dict[str, Any]]:
        """獲取結果文件信息"""
        
        files_info = []
        
        if result_dir.exists():
            for file_path in result_dir.rglob('*'):
                if file_path.is_file():
                    files_info.append({
                        'name': file_path.name,
                        'path': str(file_path.relative_to(result_dir)),
                        'size': file_path.stat().st_size,
                        'modified_time': datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
                    })
        
        return files_info

    async def _calculate_total_storage_size(self) -> int:
        """計算總存儲大小"""
        
        total_size = 0
        
        if self.base_path.exists():
            for file_path in self.base_path.rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        
        return total_size


class ResultQueryService:
    """分析結果查詢服務"""

    def __init__(self, db: Session):
        self.db = db
        self.storage_service = ResultStorageService(db)

    async def get_result_by_id(self, result_id: str, include_content: bool = False) -> Optional[Dict[str, Any]]:
        """根據ID獲取結果"""

        if include_content:
            return await self.storage_service.get_result_content(result_id)
        else:
            result = await self.storage_service.get_analysis_result(result_id)
            return result.to_dict() if result else None

    async def get_result_by_task_id(self, task_id: str, include_content: bool = False) -> Optional[Dict[str, Any]]:
        """根據任務ID獲取結果"""

        try:
            # 查找與任務ID關聯的結果
            result = self.db.query(AnalysisResult).filter(
                and_(
                    AnalysisResult.task_id == task_id,
                    AnalysisResult.is_deleted == False
                )
            ).order_by(AnalysisResult.created_time.desc()).first()

            if not result:
                return None

            if include_content:
                return await self.storage_service.get_result_content(result.result_id)
            else:
                return result.to_dict()

        except Exception as e:
            logger.error(f"根據任務ID獲取結果失敗: {e}")
            return None

    async def get_results_by_purchase(
        self,
        purchase_id: str,
        filters: Optional[Dict[str, Any]] = None,
        sort_by: str = "created_time",
        sort_order: str = "desc",
        skip: int = 0,
        limit: int = 100
    ) -> Dict[str, Any]:
        """獲取購案的結果列表"""

        # 應用篩選器
        result_type = None
        status = None

        if filters:
            if 'result_type' in filters:
                try:
                    result_type = ResultType(filters['result_type'])
                except ValueError:
                    pass

            if 'status' in filters:
                try:
                    status = ResultStatus(filters['status'])
                except ValueError:
                    pass

        # 獲取結果
        results = await self.storage_service.get_purchase_results(
            purchase_id=purchase_id,
            result_type=result_type,
            status=status,
            skip=skip,
            limit=limit
        )

        # 轉換為字典格式
        result_dicts = [result.to_dict() for result in results]

        # 排序
        if sort_by in ['created_time', 'updated_time', 'confidence_score', 'quality_score']:
            reverse = sort_order.lower() == 'desc'
            result_dicts.sort(
                key=lambda x: x.get(sort_by, 0) or 0,
                reverse=reverse
            )

        # 獲取總數
        total_count = self.db.query(AnalysisResult).filter(
            and_(
                AnalysisResult.purchase_id == purchase_id,
                AnalysisResult.is_deleted == False
            )
        ).count()

        return {
            'results': result_dicts,
            'total_count': total_count,
            'page_info': {
                'skip': skip,
                'limit': limit,
                'has_more': skip + len(result_dicts) < total_count
            }
        }

    async def search_results_advanced(
        self,
        search_params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """高級搜索結果"""

        query = self.db.query(AnalysisResult).filter(AnalysisResult.is_deleted == False)

        # 關鍵詞搜索
        if 'keywords' in search_params and search_params['keywords']:
            keywords = search_params['keywords']
            search_pattern = f"%{keywords}%"
            query = query.filter(
                or_(
                    AnalysisResult.title.like(search_pattern),
                    AnalysisResult.description.like(search_pattern),
                    AnalysisResult.summary.like(search_pattern),
                    AnalysisResult.content.like(search_pattern)
                )
            )

        # 購案篩選
        if 'purchase_id' in search_params:
            query = query.filter(AnalysisResult.purchase_id == search_params['purchase_id'])

        # 結果類型篩選
        if 'result_type' in search_params:
            try:
                result_type = ResultType(search_params['result_type'])
                query = query.filter(AnalysisResult.result_type == result_type)
            except ValueError:
                pass

        # 狀態篩選
        if 'status' in search_params:
            try:
                status = ResultStatus(search_params['status'])
                query = query.filter(AnalysisResult.status == status)
            except ValueError:
                pass

        # 信心度篩選
        if 'min_confidence' in search_params:
            query = query.filter(AnalysisResult.confidence_score >= search_params['min_confidence'])

        if 'max_confidence' in search_params:
            query = query.filter(AnalysisResult.confidence_score <= search_params['max_confidence'])

        # 時間範圍篩選
        if 'start_date' in search_params:
            try:
                start_date = datetime.fromisoformat(search_params['start_date'])
                query = query.filter(AnalysisResult.created_time >= start_date)
            except ValueError:
                pass

        if 'end_date' in search_params:
            try:
                end_date = datetime.fromisoformat(search_params['end_date'])
                query = query.filter(AnalysisResult.created_time <= end_date)
            except ValueError:
                pass

        # 標籤篩選
        if 'tags' in search_params and search_params['tags']:
            # 這裡需要JSON查詢，具體實現取決於數據庫
            pass

        # 排序
        sort_by = search_params.get('sort_by', 'created_time')
        sort_order = search_params.get('sort_order', 'desc')

        if sort_by == 'created_time':
            query = query.order_by(desc(AnalysisResult.created_time) if sort_order == 'desc' else asc(AnalysisResult.created_time))
        elif sort_by == 'confidence_score':
            query = query.order_by(desc(AnalysisResult.confidence_score) if sort_order == 'desc' else asc(AnalysisResult.confidence_score))
        elif sort_by == 'quality_score':
            query = query.order_by(desc(AnalysisResult.quality_score) if sort_order == 'desc' else asc(AnalysisResult.quality_score))

        # 分頁
        skip = search_params.get('skip', 0)
        limit = search_params.get('limit', 50)

        total_count = query.count()
        results = query.offset(skip).limit(limit).all()

        return {
            'results': [result.to_dict() for result in results],
            'total_count': total_count,
            'search_params': search_params,
            'page_info': {
                'skip': skip,
                'limit': limit,
                'has_more': skip + len(results) < total_count
            }
        }

    async def get_similar_results(
        self,
        result_id: str,
        similarity_threshold: float = 0.7,
        max_results: int = 10
    ) -> List[Dict[str, Any]]:
        """獲取相似結果"""

        # 獲取目標結果
        target_result = await self.storage_service.get_analysis_result(result_id)
        if not target_result:
            return []

        # 簡單的相似度計算（基於關鍵詞和實體）
        similar_results = []

        # 獲取同購案的其他結果
        other_results = await self.storage_service.get_purchase_results(
            target_result.purchase_id,
            limit=100
        )

        for result in other_results:
            if result.result_id == result_id:
                continue

            similarity_score = await self._calculate_similarity(target_result, result)

            if similarity_score >= similarity_threshold:
                result_dict = result.to_dict()
                result_dict['similarity_score'] = similarity_score
                similar_results.append(result_dict)

        # 按相似度排序
        similar_results.sort(key=lambda x: x['similarity_score'], reverse=True)

        return similar_results[:max_results]

    async def _calculate_similarity(self, result1: AnalysisResult, result2: AnalysisResult) -> float:
        """計算兩個結果的相似度"""

        similarity_score = 0.0

        # 關鍵詞相似度
        if result1.keywords and result2.keywords:
            keywords1 = set(result1.keywords)
            keywords2 = set(result2.keywords)

            if keywords1 and keywords2:
                intersection = len(keywords1.intersection(keywords2))
                union = len(keywords1.union(keywords2))
                keyword_similarity = intersection / union if union > 0 else 0
                similarity_score += keyword_similarity * 0.4

        # 實體相似度
        if result1.entities and result2.entities:
            entities1 = set(entity.get('name', '') for entity in result1.entities)
            entities2 = set(entity.get('name', '') for entity in result2.entities)

            if entities1 and entities2:
                intersection = len(entities1.intersection(entities2))
                union = len(entities1.union(entities2))
                entity_similarity = intersection / union if union > 0 else 0
                similarity_score += entity_similarity * 0.4

        # 主題相似度
        if result1.topics and result2.topics:
            topics1 = set(result1.topics)
            topics2 = set(result2.topics)

            if topics1 and topics2:
                intersection = len(topics1.intersection(topics2))
                union = len(topics1.union(topics2))
                topic_similarity = intersection / union if union > 0 else 0
                similarity_score += topic_similarity * 0.2

        return min(similarity_score, 1.0)


def get_result_storage_service(db: Session = None) -> ResultStorageService:
    """獲取結果儲存服務實例"""
    if db is None:
        db = next(get_db())
    return ResultStorageService(db)


def get_result_query_service(db: Session = None) -> ResultQueryService:
    """獲取結果查詢服務實例"""
    if db is None:
        db = next(get_db())
    return ResultQueryService(db)
