<template>
    <div class="stats-content">
      <el-table :data="displayItems" v-loading="loading">
        <el-table-column prop="title" label="審查要項" sortable></el-table-column>
        <el-table-column prop="memo" label="審查方式" sortable></el-table-column>
        <el-table-column prop="status" label="作業狀態" sortable>
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button-group>
              <el-button
                icon="InfoFilled"
                size="small"
                @click="handleShowResult(scope.row)"
                :disabled="!canShowResult(scope.row)"
              >
                查看結果
              </el-button>
              <el-button
                v-if="scope.row.task_id && ['failed', 'completed', 'cancelled'].includes(scope.row.status)"
                type="success"
                size="small"
                icon="Refresh"
                @click="handleRestartTask(scope.row)"
                :loading="restartingTasks.has(scope.row.task_id)"
              >
                重啟
              </el-button>
              <el-button
                v-if="scope.row.task_id && scope.row.status === 'pending'"
                type="primary"
                size="small"
                icon="VideoPlay"
                @click="handleStartTask(scope.row)"
                :loading="startingTasks.has(scope.row.task_id)"
              >
                啟動
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 使用新的任務結果查看器 -->
      <TaskResultViewer
        v-model:visible="showResultDialog"
        :task-id="selectedTaskId"
        :result-id="selectedResultId"
        :task-title="selectedTaskTitle"
        @close="handleResultDialogClose"
      />
    </div>
  </template>

  <script setup lang="ts">
  import axios from 'axios'
  import { onMounted, ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { taskAPI, analysisResultsAPI } from '@/services/api'
  import TaskResultViewer from './TaskResultViewer.vue'

  // Props
  interface Props {
    statistics?: any
    parseMethod?: string
    status?: string
    showDetailed?: boolean
    items?: any[]
    showInfoDialog?: boolean
    purchaseId?: string
  }

  const props = withDefaults(defineProps<Props>(), {
    statistics: null,
    parseMethod: 'text',
    status: 'completed',
    showDetailed: true,
    items: undefined,
    showInfoDialog: false,
    purchaseId: ''
  })

  // 響應式數據
  const resultInfo = ref("")
  const showInfoDialog = ref(false)
  const restartingTasks = ref(new Set<string>())
  const startingTasks = ref(new Set<string>())

  // 新的結果查看器相關數據
  const showResultDialog = ref(false)
  const selectedTaskId = ref('')
  const selectedResultId = ref('')
  const selectedTaskTitle = ref('')
  const taskItems = ref<any[]>([])
  const loading = ref(false)

  // Emits
  const emit = defineEmits<{
    taskRestarted: [taskId: string]
    taskStarted: [taskId: string]
  }>()

  // 計算屬性
  const displayItems = computed(() => {
    // 如果有傳入items，使用傳入的items；否則使用從API載入的taskItems
    return props.items || taskItems.value
  })

  // 方法
  // 載入購案任務
  const loadPurchaseTasks = async () => {
    if (!props.purchaseId) {
      console.log('⚠️ 沒有購案ID，無法載入任務')
      return
    }

    loading.value = true
    try {
      console.log('📋 開始載入購案任務:', props.purchaseId)
      const response = await taskAPI.getPurchaseTasks(props.purchaseId)

      const tasks = response.data.tasks || []
      console.log('📋 獲取到任務列表:', tasks.length, '個任務')

      // 轉換任務格式以符合表格顯示需求
      taskItems.value = tasks.map((task: any) => ({
        task_id: task.task_id,
        title: task.task_name,
        memo: task.description || '審查任務',
        status: task.status,
        result: task.result_data ? task.task_id : null, // 如果有結果數據，使用task_id作為結果標識
        result_id: task.result_data?.result_id || null,
        progress: task.progress,
        current_step: task.current_step,
        created_time: task.created_time,
        updated_time: task.updated_time
      }))

      console.log('✅ 任務列表轉換完成:', taskItems.value)
    } catch (error: any) {
      console.error('❌ 載入購案任務失敗:', error)
      ElMessage.error(`載入任務失敗: ${error.response?.data?.detail || error.message}`)
    } finally {
      loading.value = false
    }
  }

  // 檢查是否可以查看結果
  const canShowResult = (item: any): boolean => {
    return item.status === 'completed' && (item.result || item.result_id)
  }

  // 處理查看結果
  const handleShowResult = (item: any) => {
    if (!canShowResult(item)) {
      ElMessage.warning('該任務尚未完成或沒有結果')
      return
    }

    selectedTaskId.value = item.task_id
    selectedResultId.value = item.result_id || ''
    selectedTaskTitle.value = item.title || '任務結果'
    showResultDialog.value = true
  }

  // 處理結果對話框關閉
  const handleResultDialogClose = () => {
    showResultDialog.value = false
    selectedTaskId.value = ''
    selectedResultId.value = ''
    selectedTaskTitle.value = ''
  }
  const handleShow = async (showurl: string): Promise<string> => {
    let result = "";

    try {
      const response = await fetch(showurl); // make a GET request to showurl
      result = await response.text(); // convert the response to text and assign it to result
      console.log(result); // log the data to the console
    } catch (error) {
      console.error('Error:', error); // catch any errors and log them to the console
    }

    resultInfo.value = result
    showInfoDialog.value = true

    return result;
  }

  // 重啟任務
  const handleRestartTask = async (item: any) => {
    if (!item.task_id) {
      ElMessage.error('任務ID不存在')
      return
    }

    restartingTasks.value.add(item.task_id)

    try {
      // 檢查是否為模擬任務
      if (item.task_id.startsWith('mock-task-')) {
        // 模擬重啟過程
        console.log(`🔄 模擬重啟任務: ${item.task_id} - ${item.title}`)
        await new Promise(resolve => setTimeout(resolve, 1000)) // 模擬API延遲

        ElMessage.success(`任務 "${item.title}" 重啟成功 (模擬)`)
        emit('taskRestarted', item.task_id)

        // 更新本地狀態
        item.status = 'pending'
      } else {
        // 真實任務重啟
        await taskAPI.restartTask(item.task_id, {
          force: true,
          reset_progress: true,
          priority_boost: 1
        })

        ElMessage.success(`任務 "${item.title}" 重啟成功`)
        emit('taskRestarted', item.task_id)

        // 更新本地狀態
        item.status = 'pending'
      }

    } catch (error: any) {
      console.error('重啟任務失敗:', error)
      ElMessage.error(`重啟任務失敗: ${error.response?.data?.detail || error.message}`)
    } finally {
      restartingTasks.value.delete(item.task_id)
    }
  }

  // 啟動任務
  const handleStartTask = async (item: any) => {
    if (!item.task_id) {
      ElMessage.error('任務ID不存在')
      return
    }

    startingTasks.value.add(item.task_id)

    try {
      // 檢查是否為模擬任務
      if (item.task_id.startsWith('mock-task-')) {
        // 模擬啟動過程
        console.log(`▶️ 模擬啟動任務: ${item.task_id} - ${item.title}`)
        await new Promise(resolve => setTimeout(resolve, 1000)) // 模擬API延遲

        ElMessage.success(`任務 "${item.title}" 啟動成功 (模擬)`)
        emit('taskStarted', item.task_id)

        // 更新本地狀態
        item.status = 'running'
      } else {
        // 真實任務啟動
        await taskAPI.startTask(item.task_id, {
          force: false,
          priority_boost: 1
        })

        ElMessage.success(`任務 "${item.title}" 啟動成功`)
        emit('taskStarted', item.task_id)

        // 更新本地狀態
        item.status = 'running'
      }

    } catch (error: any) {
      console.error('啟動任務失敗:', error)
      ElMessage.error(`啟動任務失敗: ${error.response?.data?.detail || error.message}`)
    } finally {
      startingTasks.value.delete(item.task_id)
    }
  }

  const getStatusType = (status: string): string => {
    const statusMap: Record<string, string> = {
      pending: 'warning',
      processing: 'warning',
      running: 'warning',
      completed: 'success',
      failed: 'danger',
      cancelled: 'info',
      '等待中': 'warning',
      '執行中': 'warning',
      '處理中': 'warning',
      '已完成': 'success',
      '失敗': 'danger',
      '已取消': 'info'
    }
    return statusMap[status] || 'info'
  }

  const getStatusText = (status: string): string => {
    const statusMap: Record<string, string> = {
      pending: '等待中',
      processing: '處理中',
      running: '執行中',
      completed: '已完成',
      failed: '失敗',
      cancelled: '已取消'
    }
    return statusMap[status] || status
  }

  // 生命週期鉤子
  onMounted(() => {
    console.log('🎨 PreviewItemTable 組件掛載')
    if (props.purchaseId && !props.items) {
      console.log('📋 開始載入購案任務:', props.purchaseId)
      loadPurchaseTasks()
    }
  })

  // 監聽購案ID變化
  watch(() => props.purchaseId, (newPurchaseId) => {
    if (newPurchaseId && !props.items) {
      console.log('🔄 購案ID變化，重新載入任務:', newPurchaseId)
      loadPurchaseTasks()
    }
  }, { immediate: false })

  // 監聽items變化
  watch(() => props.items, (newItems) => {
    if (newItems) {
      console.log('📋 使用傳入的items:', newItems.length, '個項目')
    }
  }, { immediate: true })
  </script>

  <style scoped>
  .stats-content {
    padding: 20px 0;
  }

  .stat-card {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    background: #e9ecef;
    transform: translateY(-2px);
  }

  .detailed-stats {
    margin-top: 30px;
  }

  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #ffffff;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    margin-bottom: 10px;
  }

  .stat-label {
    font-weight: 500;
    color: #606266;
  }

  .stat-value {
    font-weight: 600;
    color: #303133;
  }

  /* 響應式設計 */
  @media (max-width: 768px) {
    .stats-content .el-col {
      margin-bottom: 15px;
    }

    .stat-item {
      flex-direction: column;
      gap: 5px;
      text-align: center;
    }
  }
  </style>
