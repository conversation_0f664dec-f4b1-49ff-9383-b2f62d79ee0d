import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: { title: '首頁' }
    },
    {
      path: '/upload',
      name: 'upload',
      component: () => import('../views/PurchaseAnalysisView.vue'),
      meta: { title: '購案分析' }
    },
    {
      path: '/purchases',
      name: 'purchases',
      component: () => import('../views/PurchaseManagementView.vue'),
      meta: { title: '購案管理' }
    },
    {
      path: '/upload-legacy',
      name: 'upload-legacy',
      component: () => import('../views/UploadView.vue'),
      meta: { title: 'PDF 上傳 (舊版)' }
    },
    {
      path: '/upload-demo',
      name: 'upload-demo',
      component: () => import('../views/PDFUploadDemo.vue'),
      meta: { title: 'PDF上傳組件演示' }
    },
    {
      path: '/knowledge',
      name: 'knowledge',
      component: () => import('../views/KnowledgeView.vue'),
      meta: { title: '知識庫' }
    },
    {
      path: '/api-test',
      name: 'api-test',
      component: () => import('../views/ApiTestView.vue'),
      meta: { title: 'API 測試' }
    },
    {
      path: '/training',
      name: 'training',
      component: () => import('../views/TrainingView.vue'),
      meta: { title: 'GraphRAG 訓練' }
    },
    {
      path: '/results/:taskId?',
      name: 'results',
      component: () => import('../views/ResultsView.vue'),
      meta: { title: '解析結果' }
    },
    {
      path: '/results',
      name: 'results-query',
      component: () => import('../views/ResultsView.vue'),
      meta: { title: '解析結果' }
    },
    {
      path: '/tasks',
      name: 'tasks',
      component: () => import('../views/TaskManagementView.vue'),
      meta: { title: '任務管理' }
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
      meta: { title: '關於系統' }
    },
  ],
})

export default router
