"""
分析結果管理API端點
"""

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
import logging

from app.core.database import get_db
from app.models.analysis_result import ResultType, ResultStatus, ConfidenceLevel
from app.services.result_storage_service import get_result_storage_service, get_result_query_service
from app.services.result_storage_service import ResultStorageService, ResultQueryService

logger = logging.getLogger(__name__)

router = APIRouter()


# 請求模型
class CreateResultRequest(BaseModel):
    purchase_id: str = Field(..., description="購案ID")
    task_id: Optional[str] = Field(None, description="任務ID")
    title: str = Field(..., description="結果標題")
    description: Optional[str] = Field(None, description="結果描述")
    result_type: str = Field("analysis", description="結果類型")
    content_data: Dict[str, Any] = Field(..., description="內容數據")
    analysis_metadata: Optional[Dict[str, Any]] = Field(None, description="分析元數據")
    files: Optional[List[Dict[str, Any]]] = Field(None, description="相關文件")


class UpdateResultRequest(BaseModel):
    title: Optional[str] = Field(None, description="結果標題")
    description: Optional[str] = Field(None, description="結果描述")
    summary: Optional[str] = Field(None, description="摘要")
    content: Optional[str] = Field(None, description="內容")
    key_findings: Optional[List[Dict[str, Any]]] = Field(None, description="關鍵發現")
    recommendations: Optional[List[Dict[str, Any]]] = Field(None, description="建議")
    confidence_score: Optional[float] = Field(None, description="信心度評分")
    quality_score: Optional[float] = Field(None, description="質量評分")
    relevance_score: Optional[float] = Field(None, description="相關性評分")
    tags: Optional[List[str]] = Field(None, description="標籤")
    categories: Optional[List[str]] = Field(None, description="分類")
    priority: Optional[str] = Field(None, description="優先級")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元數據")


class SearchResultsRequest(BaseModel):
    keywords: Optional[str] = Field(None, description="關鍵詞")
    purchase_id: Optional[str] = Field(None, description="購案ID")
    result_type: Optional[str] = Field(None, description="結果類型")
    status: Optional[str] = Field(None, description="狀態")
    min_confidence: Optional[float] = Field(None, description="最小信心度")
    max_confidence: Optional[float] = Field(None, description="最大信心度")
    start_date: Optional[str] = Field(None, description="開始日期")
    end_date: Optional[str] = Field(None, description="結束日期")
    tags: Optional[List[str]] = Field(None, description="標籤")
    sort_by: Optional[str] = Field("created_time", description="排序字段")
    sort_order: Optional[str] = Field("desc", description="排序順序")
    skip: Optional[int] = Field(0, description="跳過數量")
    limit: Optional[int] = Field(50, description="限制數量")


# 響應模型
class ResultResponse(BaseModel):
    result_id: str
    purchase_id: str
    task_id: Optional[str]
    title: str
    description: Optional[str]
    result_type: str
    status: str
    summary: Optional[str]
    confidence_score: Optional[float]
    confidence_level: Optional[str]
    quality_score: Optional[float]
    relevance_score: Optional[float]
    created_time: str
    updated_time: Optional[str]
    word_count: int
    view_count: int


@router.post("/results", response_model=ResultResponse)
async def create_analysis_result(
    request: CreateResultRequest,
    db: Session = Depends(get_db)
):
    """創建分析結果"""
    
    try:
        storage_service = get_result_storage_service(db)
        
        # 驗證結果類型
        try:
            result_type = ResultType(request.result_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"不支持的結果類型: {request.result_type}")
        
        # 創建結果
        result = await storage_service.store_analysis_result(
            purchase_id=request.purchase_id,
            task_id=request.task_id,
            title=request.title,
            description=request.description,
            result_type=result_type,
            content_data=request.content_data,
            analysis_metadata=request.analysis_metadata,
            files=request.files
        )
        
        return ResultResponse(
            result_id=result.result_id,
            purchase_id=result.purchase_id,
            task_id=result.task_id,
            title=result.title,
            description=result.description,
            result_type=result.result_type.value,
            status=result.status.value,
            summary=result.summary,
            confidence_score=result.confidence_score,
            confidence_level=result.confidence_level.value if result.confidence_level else None,
            quality_score=result.quality_score,
            relevance_score=result.relevance_score,
            created_time=result.created_time.isoformat(),
            updated_time=result.updated_time.isoformat() if result.updated_time else None,
            word_count=result.word_count,
            view_count=result.view_count
        )
        
    except Exception as e:
        logger.error(f"創建分析結果失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/results/{result_id}", response_model=Dict[str, Any])
async def get_analysis_result(
    result_id: str,
    include_content: bool = Query(False, description="是否包含詳細內容"),
    db: Session = Depends(get_db)
):
    """獲取分析結果"""
    
    try:
        query_service = get_result_query_service(db)
        
        result = await query_service.get_result_by_id(result_id, include_content)
        
        if not result:
            raise HTTPException(status_code=404, detail="分析結果不存在")
        
        # 更新查看次數
        if include_content:
            storage_service = get_result_storage_service(db)
            result_obj = await storage_service.get_analysis_result(result_id)
            if result_obj:
                result_obj.increment_view_count()
                storage_service.db.commit()
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取分析結果失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/results", response_model=Dict[str, Any])
async def get_analysis_results(
    purchase_id: Optional[str] = Query(None, description="購案ID"),
    result_type: Optional[str] = Query(None, description="結果類型"),
    status: Optional[str] = Query(None, description="狀態"),
    sort_by: str = Query("created_time", description="排序字段"),
    sort_order: str = Query("desc", description="排序順序"),
    skip: int = Query(0, description="跳過數量"),
    limit: int = Query(100, description="限制數量"),
    db: Session = Depends(get_db)
):
    """獲取分析結果列表"""
    
    try:
        query_service = get_result_query_service(db)
        
        # 構建篩選器
        filters = {}
        if result_type:
            filters['result_type'] = result_type
        if status:
            filters['status'] = status
        
        if purchase_id:
            # 獲取特定購案的結果
            results = await query_service.get_results_by_purchase(
                purchase_id=purchase_id,
                filters=filters,
                sort_by=sort_by,
                sort_order=sort_order,
                skip=skip,
                limit=limit
            )
        else:
            # 搜索所有結果
            search_params = {
                'sort_by': sort_by,
                'sort_order': sort_order,
                'skip': skip,
                'limit': limit,
                **filters
            }
            results = await query_service.search_results_advanced(search_params)
        
        return results
        
    except Exception as e:
        logger.error(f"獲取分析結果列表失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/results/{result_id}", response_model=ResultResponse)
async def update_analysis_result(
    result_id: str,
    request: UpdateResultRequest,
    db: Session = Depends(get_db)
):
    """更新分析結果"""
    
    try:
        storage_service = get_result_storage_service(db)
        
        # 構建更新數據
        updates = {}
        for field, value in request.dict(exclude_unset=True).items():
            if value is not None:
                updates[field] = value
        
        # 執行更新
        result = await storage_service.update_analysis_result(result_id, updates)
        
        if not result:
            raise HTTPException(status_code=404, detail="分析結果不存在")
        
        return ResultResponse(
            result_id=result.result_id,
            purchase_id=result.purchase_id,
            task_id=result.task_id,
            title=result.title,
            description=result.description,
            result_type=result.result_type.value,
            status=result.status.value,
            summary=result.summary,
            confidence_score=result.confidence_score,
            confidence_level=result.confidence_level.value if result.confidence_level else None,
            quality_score=result.quality_score,
            relevance_score=result.relevance_score,
            created_time=result.created_time.isoformat(),
            updated_time=result.updated_time.isoformat() if result.updated_time else None,
            word_count=result.word_count,
            view_count=result.view_count
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新分析結果失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/results/{result_id}")
async def delete_analysis_result(
    result_id: str,
    hard_delete: bool = Query(False, description="是否硬刪除"),
    db: Session = Depends(get_db)
):
    """刪除分析結果"""
    
    try:
        storage_service = get_result_storage_service(db)
        
        success = await storage_service.delete_analysis_result(result_id, hard_delete)
        
        if not success:
            raise HTTPException(status_code=404, detail="分析結果不存在")
        
        return {
            "message": f"分析結果已{'硬' if hard_delete else '軟'}刪除",
            "result_id": result_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刪除分析結果失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/results/search", response_model=Dict[str, Any])
async def search_analysis_results(
    request: SearchResultsRequest,
    db: Session = Depends(get_db)
):
    """搜索分析結果"""
    
    try:
        query_service = get_result_query_service(db)
        
        # 轉換為搜索參數
        search_params = request.dict(exclude_unset=True)
        
        results = await query_service.search_results_advanced(search_params)
        
        return results
        
    except Exception as e:
        logger.error(f"搜索分析結果失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/results/{result_id}/similar")
async def get_similar_results(
    result_id: str,
    similarity_threshold: float = Query(0.7, description="相似度閾值"),
    max_results: int = Query(10, description="最大結果數"),
    db: Session = Depends(get_db)
):
    """獲取相似結果"""
    
    try:
        query_service = get_result_query_service(db)
        
        similar_results = await query_service.get_similar_results(
            result_id=result_id,
            similarity_threshold=similarity_threshold,
            max_results=max_results
        )
        
        return {
            "result_id": result_id,
            "similar_results": similar_results,
            "count": len(similar_results)
        }
        
    except Exception as e:
        logger.error(f"獲取相似結果失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/results/{result_id}/export")
async def export_analysis_result(
    result_id: str,
    export_format: str = Query("json", description="導出格式"),
    include_files: bool = Query(True, description="是否包含文件"),
    db: Session = Depends(get_db)
):
    """導出分析結果"""
    
    try:
        storage_service = get_result_storage_service(db)
        
        export_data = await storage_service.export_result(
            result_id=result_id,
            export_format=export_format,
            include_files=include_files
        )
        
        if not export_data:
            raise HTTPException(status_code=404, detail="分析結果不存在")
        
        # 更新下載次數
        result = await storage_service.get_analysis_result(result_id)
        if result:
            result.increment_download_count()
            storage_service.db.commit()
        
        return export_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"導出分析結果失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics")
async def get_results_statistics(
    db: Session = Depends(get_db)
):
    """獲取結果統計信息"""
    
    try:
        storage_service = get_result_storage_service(db)
        
        stats = await storage_service.get_result_statistics()
        
        return stats
        
    except Exception as e:
        logger.error(f"獲取結果統計失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/types")
async def get_result_types():
    """獲取可用的結果類型"""
    
    return {
        "result_types": [
            {
                "value": result_type.value,
                "name": result_type.value.replace('_', ' ').title(),
                "description": f"{result_type.value} 類型的分析結果"
            }
            for result_type in ResultType
        ]
    }


@router.get("/statuses")
async def get_result_statuses():
    """獲取可用的結果狀態"""
    
    return {
        "statuses": [
            {
                "value": status.value,
                "name": status.value.replace('_', ' ').title(),
                "description": f"結果狀態: {status.value}"
            }
            for status in ResultStatus
        ]
    }


@router.get("/tasks/{task_id}/result", response_model=Dict[str, Any])
async def get_task_result(
    task_id: str,
    include_content: bool = Query(True, description="是否包含詳細內容"),
    db: Session = Depends(get_db)
):
    """通過任務ID獲取分析結果"""

    try:
        query_service = get_result_query_service(db)

        # 通過任務ID查找結果
        result = await query_service.get_result_by_task_id(task_id, include_content)

        if not result:
            raise HTTPException(status_code=404, detail="該任務沒有分析結果")

        # 更新查看次數
        if include_content and result.get('result_id'):
            storage_service = get_result_storage_service(db)
            result_obj = await storage_service.get_analysis_result(result['result_id'])
            if result_obj:
                result_obj.increment_view_count()
                storage_service.db.commit()

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"通過任務ID獲取分析結果失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))
