#!/usr/bin/env python3
"""
清理舊任務
"""

import sys
from pathlib import Path

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, str(Path(__file__).parent))

from app.core.database import get_db
from app.models.analysis_task import AnalysisTask
from app.models.purchase import Purchase
from app.models.file import FileRecord
from app.models.analysis_result import AnalysisResult
from app.models.rag_database import RAGDatabase

def cleanup_old_data():
    """清理舊數據"""

    db = None
    try:
        db = next(get_db())

        # 按照外鍵依賴關係的正確順序刪除

        # 1. 先刪除分析結果（依賴於任務和購案）
        result_count = db.query(AnalysisResult).count()
        if result_count > 0:
            print(f"刪除 {result_count} 個分析結果...")
            db.query(AnalysisResult).delete()

        # 2. 刪除分析任務（依賴於購案和文件）
        task_count = db.query(AnalysisTask).count()
        if task_count > 0:
            print(f"刪除 {task_count} 個分析任務...")
            db.query(AnalysisTask).delete()

        # 3. 刪除RAG資料庫（依賴於購案）
        rag_count = db.query(RAGDatabase).count()
        if rag_count > 0:
            print(f"刪除 {rag_count} 個RAG資料庫記錄...")
            db.query(RAGDatabase).delete()

        # 4. 刪除文件記錄（依賴於購案）
        file_count = db.query(FileRecord).count()
        if file_count > 0:
            print(f"刪除 {file_count} 個文件記錄...")
            db.query(FileRecord).delete()

        # 5. 最後刪除購案（被其他表依賴）
        purchase_count = db.query(Purchase).count()
        if purchase_count > 0:
            print(f"刪除 {purchase_count} 個購案記錄...")
            db.query(Purchase).delete()

        db.commit()
        print("✅ 清理完成")

    except Exception as e:
        print(f"❌ 清理失敗: {e}")
        if db:
            db.rollback()
        raise
    finally:
        if db:
            db.close()

if __name__ == "__main__":
    cleanup_old_data()
