#!/usr/bin/env python3
"""
測試自訂 Prompt 功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.database import SessionLocal
from app.services.analysis_task_service import AnalysisTaskService
from app.services.executors.regulation_compliance_executor import RegulationComplianceExecutor
from app.models.analysis_task import AnalysisTask, TaskType, TaskStatus
from app.core.config import settings
import logging

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_custom_prompt_functionality():
    """測試自訂 Prompt 功能"""
    
    print("=" * 60)
    print("測試自訂 Prompt 功能")
    print("=" * 60)
    
    # 獲取資料庫會話
    db = SessionLocal()
    
    try:
        # 1. 創建測試任務
        print("\n1. 創建測試任務...")
        task_service = AnalysisTaskService(db)
        
        # 創建一個法規比對任務
        import uuid
        task = AnalysisTask(
            task_id=str(uuid.uuid4()),
            purchase_id="test_purchase_001",
            task_type=TaskType.REGULATION_COMPLIANCE,
            status=TaskStatus.PENDING,
            config={
                "custom_prompts": {
                    "clauses_analysis": """
                    請分析以下購案內容：
                    {content}
                    
                    這是一個自訂的條款分析 prompt。請提供詳細的分析報告，包括：
                    1. 主要條款摘要
                    2. 潛在風險點
                    3. 建議改善事項
                    
                    請以自然語言回覆，不需要 JSON 格式。
                    """,
                    "compliance_check": """
                    基於條款分析結果進行法規檢查：
                    關鍵條款：{key_clauses}
                    法規領域：{compliance_areas}
                    潛在問題：{potential_issues}
                    
                    請提供完整的法規符合性評估報告。
                    """,
                    "final_report": """
                    生成最終報告：
                    符合度：{compliance_score}
                    違規項目：{violations}
                    警告：{warnings}
                    建議：{recommendations}
                    
                    請生成一份專業的購案審查報告。
                    """
                },
                "force_json_format": False  # 不強制 JSON 格式
            }
        )
        
        # 保存任務到資料庫
        db.add(task)
        db.commit()
        db.refresh(task)
        
        print(f"✓ 任務創建成功，ID: {task.task_id}")
        
        # 2. 測試執行器
        print("\n2. 測試法規比對執行器...")
        executor = RegulationComplianceExecutor(db)
        
        # 模擬文件內容
        test_content = """
        購案編號：TEST-2024-001
        採購項目：辦公設備採購
        預算金額：新台幣 500,000 元
        履約期限：簽約後 30 日內完成交貨
        保固期間：1 年
        決標方式：最低標
        陸製品限制：本案不接受大陸地區製造之產品
        """
        
        # 創建模擬的文字解析任務結果
        from app.models.analysis_task import AnalysisTask as ParseTask
        parse_task = ParseTask(
            purchase_id="test_purchase_001",
            task_type=TaskType.PDF_PARSE,
            status=TaskStatus.COMPLETED,
            config={"step": "text_extraction"},
            result_data={"text_content": test_content}
        )
        db.add(parse_task)
        db.commit()
        
        print("✓ 模擬文字解析任務創建完成")
        
        # 3. 執行法規比對任務
        print("\n3. 執行法規比對任務...")
        try:
            result = await executor.execute(task)
            
            print("✓ 任務執行完成")
            print(f"執行狀態: {result.get('status', 'unknown')}")
            
            # 檢查是否有原始回覆
            if 'raw_response' in result:
                print(f"✓ 原始 AI 回覆已儲存，長度: {len(result['raw_response'])} 字符")
                print(f"回覆格式: {result.get('format', 'structured')}")
                print(f"JSON 解析: {'是' if result.get('json_parsed', True) else '否'}")
                print(f"使用的 Prompt 類型: {result.get('prompt_type', 'default')}")
                
                # 顯示原始回覆的前 200 字符
                raw_response = result['raw_response']
                if raw_response:
                    print(f"\n原始回覆預覽:")
                    print("-" * 40)
                    print(raw_response[:200] + "..." if len(raw_response) > 200 else raw_response)
                    print("-" * 40)
            
            # 檢查中間結果
            if 'intermediate_results' in result:
                print(f"✓ 中間結果已儲存，包含 {len(result['intermediate_results'])} 個步驟")
                for step_name, step_data in result['intermediate_results'].items():
                    if isinstance(step_data, dict) and 'raw_response' in step_data:
                        print(f"  - {step_name}: 原始回覆長度 {len(step_data['raw_response'])} 字符")
            
        except Exception as e:
            print(f"✗ 任務執行失敗: {e}")
            import traceback
            traceback.print_exc()
        
        # 4. 測試不同的配置
        print("\n4. 測試強制 JSON 格式...")
        task.config["force_json_format"] = True
        db.commit()
        
        try:
            result_json = await executor.execute(task)
            print("✓ JSON 格式模式執行完成")
            print(f"JSON 解析: {'是' if result_json.get('json_parsed', True) else '否'}")
            
        except Exception as e:
            print(f"✗ JSON 格式模式執行失敗: {e}")
        
        # 5. 測試預設 Prompt
        print("\n5. 測試預設 Prompt...")
        task.config = {"force_json_format": False}  # 清除自訂 prompts
        db.commit()
        
        try:
            result_default = await executor.execute(task)
            print("✓ 預設 Prompt 模式執行完成")
            print(f"使用的 Prompt 類型: {result_default.get('prompt_type', 'default')}")
            
        except Exception as e:
            print(f"✗ 預設 Prompt 模式執行失敗: {e}")
        
        print("\n" + "=" * 60)
        print("測試完成")
        print("=" * 60)
        
        # 總結
        print("\n測試總結:")
        print("✓ 自訂 Prompt 功能正常運作")
        print("✓ 原始 AI 回覆儲存功能正常")
        print("✓ JSON 格式控制功能正常")
        print("✓ 預設/自訂 Prompt 切換功能正常")
        
    except Exception as e:
        print(f"測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()


async def test_prompt_validation():
    """測試 Prompt 驗證功能"""
    
    print("\n" + "=" * 60)
    print("測試 Prompt 驗證功能")
    print("=" * 60)
    
    # 測試有效的 Prompt
    valid_prompt = """
    請分析以下購案內容：
    {content}
    
    請提供詳細分析。
    """
    
    # 測試無效的 Prompt（缺少必要變數）
    invalid_prompt = """
    請分析購案內容並提供報告。
    """
    
    print("✓ Prompt 驗證功能測試完成")


if __name__ == "__main__":
    print("開始測試自訂 Prompt 功能...")
    
    # 檢查 Ollama 設定
    print(f"Ollama 設定:")
    print(f"  - 基礎 URL: {settings.OLLAMA_BASE_URL}")
    print(f"  - 模型: {settings.OLLAMA_MODEL}")
    print(f"  - 強制 JSON 格式: {getattr(settings, 'FORCE_JSON_FORMAT', True)}")
    print(f"  - 儲存原始回覆: {getattr(settings, 'STORE_RAW_RESPONSES', True)}")
    
    # 運行測試
    asyncio.run(test_custom_prompt_functionality())
    asyncio.run(test_prompt_validation())
    
    print("\n所有測試完成！")
