# Page snapshot

```yaml
- img
- heading "購案審查系統" [level=1]
- menubar:
  - menuitem "首頁":
    - img
    - text: 首頁
  - menuitem "購案分析":
    - img
    - text: 購案分析
  - menuitem "購案管理":
    - img
    - text: 購案管理
  - menuitem "組件演示":
    - img
    - text: 組件演示
  - menuitem "知識庫":
    - img
    - text: 知識庫
  - menuitem "GraphRAG 訓練":
    - img
    - text: GraphRAG 訓練
  - menuitem "任務管理":
    - img
    - text: 任務管理
  - menuitem:
    - img
- button "登入"
- main:
  - heading "購案分析系統" [level=2]
  - text: 智能購案文檔分析與知識管理
  - heading "新增購案分析" [level=3]:
    - img
    - text: 新增購案分析
  - heading "選擇分析模式" [level=4]
  - radiogroup "radio-group":
    - radio "一般 RAG" [checked]
    - img
    - text: 一般 RAG
    - radio "GraphRAG"
    - img
    - text: GraphRAG
  - paragraph: 使用向量資料庫進行語義搜索，適合快速查詢和相似度匹配
  - heading "選擇上傳模式" [level=4]
  - radiogroup "radio-group":
    - radio "單檔案上傳" [checked]
    - img
    - text: 單檔案上傳
    - radio "多檔案上傳"
    - img
    - text: 多檔案上傳
  - paragraph: 上傳單一文件進行分析，適合簡單的文檔處理
  - img
  - heading "上傳PDF文件" [level=3]
  - paragraph: 將PDF文件拖拽到此處，或點擊選擇文件
  - text: 支援格式：PDF 最大大小：50MB
  - heading "購案記錄" [level=3]:
    - img
    - text: 購案記錄
  - button "刷新":
    - img
    - text: 刷新
  - heading "PDF文件 - 20250630_185911_0236ee4e-e3b4-4a08-91dd-ffc37a912deb.pdf" [level=4]
  - text: 等待中 一般RAG
  - button "查看詳情"
  - button "更多":
    - text: 更多
    - img
  - paragraph: 無描述
  - img
  - text: 上傳時間：2025/07/15 上午03:20
  - img
  - text: 文件數量：1
  - img
  - text: 分析模式：一般RAG
  - heading "PDF文件 - fafada18-cad2-45ce-9698-5b20aae9e764_Chainlit__Semantic_Kernel__Ollama_整合指南：使用_Llama_3.2_模型.pdf" [level=4]
  - text: 等待中 一般RAG
  - button "查看詳情"
  - button "更多":
    - text: 更多
    - img
  - paragraph: 無描述
  - img
  - text: 上傳時間：2025/07/15 上午03:18
  - img
  - text: 文件數量：1
  - img
  - text: 分析模式：一般RAG
  - heading "PDF文件 - 20250630_185911_0236ee4e-e3b4-4a08-91dd-ffc37a912deb.pdf" [level=4]
  - text: 等待中 一般RAG
  - button "查看詳情"
  - button "更多":
    - text: 更多
    - img
  - paragraph: 無描述
  - img
  - text: 上傳時間：2025/07/14 上午08:48
  - img
  - text: 文件數量：1
  - img
  - text: 分析模式：一般RAG
  - heading "PDF文件 - 20250630_185911_0236ee4e-e3b4-4a08-91dd-ffc37a912deb.pdf" [level=4]
  - text: 等待中 一般RAG
  - button "查看詳情"
  - button "更多":
    - text: 更多
    - img
  - paragraph: 無描述
  - img
  - text: 上傳時間：2025/07/14 上午08:48
  - img
  - text: 文件數量：1
  - img
  - text: 分析模式：一般RAG
  - heading "PDF文件 - 20250630_185911_0236ee4e-e3b4-4a08-91dd-ffc37a912deb.pdf" [level=4]
  - text: 等待中 一般RAG
  - button "查看詳情"
  - button "更多":
    - text: 更多
    - img
  - paragraph: 無描述
  - img
  - text: 上傳時間：2025/07/14 上午08:43
  - img
  - text: 文件數量：1
  - img
  - text: 分析模式：一般RAG
  - heading "PDF文件 - 20250630_185911_0236ee4e-e3b4-4a08-91dd-ffc37a912deb.pdf" [level=4]
  - text: 等待中 一般RAG
  - button "查看詳情"
  - button "更多":
    - text: 更多
    - img
  - paragraph: 無描述
  - img
  - text: 上傳時間：2025/07/14 上午08:43
  - img
  - text: 文件數量：1
  - img
  - text: 分析模式：一般RAG
  - heading "PDF文件 - 20250630_185911_0236ee4e-e3b4-4a08-91dd-ffc37a912deb.pdf" [level=4]
  - text: 等待中 一般RAG
  - button "查看詳情"
  - button "更多":
    - text: 更多
    - img
  - paragraph: 無描述
  - img
  - text: 上傳時間：2025/07/14 上午08:43
  - img
  - text: 文件數量：1
  - img
  - text: 分析模式：一般RAG
  - heading "PDF文件 - 20250630_185911_0236ee4e-e3b4-4a08-91dd-ffc37a912deb.pdf" [level=4]
  - text: 等待中 一般RAG
  - button "查看詳情"
  - button "更多":
    - text: 更多
    - img
  - paragraph: 無描述
  - img
  - text: 上傳時間：2025/07/14 上午08:38
  - img
  - text: 文件數量：1
  - img
  - text: 分析模式：一般RAG
  - heading "PDF文件 - 20250630_185911_0236ee4e-e3b4-4a08-91dd-ffc37a912deb.pdf" [level=4]
  - text: 等待中 一般RAG
  - button "查看詳情"
  - button "更多":
    - text: 更多
    - img
  - paragraph: 無描述
  - img
  - text: 上傳時間：2025/07/14 上午08:38
  - img
  - text: 文件數量：1
  - img
  - text: 分析模式：一般RAG
  - heading "PDF文件 - 20250630_185911_0236ee4e-e3b4-4a08-91dd-ffc37a912deb.pdf" [level=4]
  - text: 等待中 一般RAG
  - button "查看詳情"
  - button "更多":
    - text: 更多
    - img
  - paragraph: 無描述
  - img
  - text: 上傳時間：2025/07/14 上午08:38
  - img
  - text: 文件數量：1
  - img
  - text: 分析模式：一般RAG
  - heading "測試購案" [level=4]
  - text: 等待中 一般RAG
  - button "查看詳情"
  - button "更多":
    - text: 更多
    - img
  - paragraph: 用於測試的購案
  - img
  - text: 上傳時間：2025/07/14 上午08:15
  - img
  - text: 文件數量：0
  - img
  - text: 分析模式：一般RAG
  - heading "測試購案" [level=4]
  - text: 等待中 一般RAG
  - button "查看詳情"
  - button "更多":
    - text: 更多
    - img
  - paragraph: 用於測試的購案
  - img
  - text: 上傳時間：2025/07/14 上午08:13
  - img
  - text: 文件數量：0
  - img
  - text: 分析模式：一般RAG
  - heading "PDF文件 - 20250630_185911_0236ee4e-e3b4-4a08-91dd-ffc37a912deb.pdf" [level=4]
  - text: 等待中 一般RAG
  - button "查看詳情"
  - button "更多":
    - text: 更多
    - img
  - paragraph: 無描述
  - img
  - text: 上傳時間：2025/07/14 上午07:46
  - img
  - text: 文件數量：1
  - img
  - text: 分析模式：一般RAG
  - heading "PDF文件 - 20250630_185911_0236ee4e-e3b4-4a08-91dd-ffc37a912deb.pdf" [level=4]
  - text: 等待中 一般RAG
  - button "查看詳情"
  - button "更多":
    - text: 更多
    - img
  - paragraph: 無描述
  - img
  - text: 上傳時間：2025/07/14 上午07:46
  - img
  - text: 文件數量：1
  - img
  - text: 分析模式：一般RAG
  - heading "PDF文件 - 20250630_185911_0236ee4e-e3b4-4a08-91dd-ffc37a912deb.pdf" [level=4]
  - text: 等待中 一般RAG
  - button "查看詳情"
  - button "更多":
    - text: 更多
    - img
  - paragraph: 無描述
  - img
  - text: 上傳時間：2025/07/14 上午07:46
  - img
  - text: 文件數量：1
  - img
  - text: 分析模式：一般RAG
  - heading "PDF文件 - fafada18-cad2-45ce-9698-5b20aae9e764_Chainlit__Semantic_Kernel__Ollama_整合指南：使用_Llama_3.2_模型.pdf" [level=4]
  - text: 等待中 一般RAG
  - button "查看詳情"
  - button "更多":
    - text: 更多
    - img
  - paragraph: 無描述
  - img
  - text: 上傳時間：2025/07/14 上午06:46
  - img
  - text: 文件數量：1
  - img
  - text: 分析模式：一般RAG
  - heading "PDF文件 - fafada18-cad2-45ce-9698-5b20aae9e764_Chainlit__Semantic_Kernel__Ollama_整合指南：使用_Llama_3.2_模型.pdf" [level=4]
  - text: 等待中 一般RAG
  - button "查看詳情"
  - button "更多":
    - text: 更多
    - img
  - paragraph: 無描述
  - img
  - text: 上傳時間：2025/07/14 上午06:43
  - img
  - text: 文件數量：1
  - img
  - text: 分析模式：一般RAG
  - heading "PDF文件 - 20250630_185911_0236ee4e-e3b4-4a08-91dd-ffc37a912deb.pdf" [level=4]
  - text: 等待中 一般RAG
  - button "查看詳情"
  - button "更多":
    - text: 更多
    - img
  - paragraph: 無描述
  - img
  - text: 上傳時間：2025/07/14 上午06:39
  - img
  - text: 文件數量：1
  - img
  - text: 分析模式：一般RAG
  - heading "PDF文件 - 20250630_185911_0236ee4e-e3b4-4a08-91dd-ffc37a912deb.pdf" [level=4]
  - text: 等待中 一般RAG
  - button "查看詳情"
  - button "更多":
    - text: 更多
    - img
  - paragraph: 無描述
  - img
  - text: 上傳時間：2025/07/14 上午06:37
  - img
  - text: 文件數量：1
  - img
  - text: 分析模式：一般RAG
  - heading "PDF文件 - fafada18-cad2-45ce-9698-5b20aae9e764_Chainlit__Semantic_Kernel__Ollama_整合指南：使用_Llama_3.2_模型.pdf" [level=4]
  - text: 等待中 一般RAG
  - button "查看詳情"
  - button "更多":
    - text: 更多
    - img
  - paragraph: 無描述
  - img
  - text: 上傳時間：2025/07/14 上午06:19
  - img
  - text: 文件數量：1
  - img
  - text: 分析模式：一般RAG
- paragraph: © 2024 購案審查系統. All rights reserved.
- paragraph: Powered by Vue.js + ElementVuePlus + GraphRAG
- img
- img
- text: "[plugin:vite:vue] v-model cannot be used on a prop, because local prop bindings are not writable. Use a v-bind binding combined with a v-on listener that emits update:x event instead. C:/home/<USER>/repo/frontend/src/components/TaskResultViewer.vue:3:14 1 | <template> 2 | <el-dialog 3 | v-model=\"visible\" | ^ 4 | :title=\"dialogTitle\" 5 | width=\"90vw\" at createCompilerError (C:\\home\\PurchaseReview\\repo\\frontend\\node_modules\\@vue\\compiler-core\\dist\\compiler-core.cjs.js:1364:17) at Object.transformModel (C:\\home\\PurchaseReview\\repo\\frontend\\node_modules\\@vue\\compiler-core\\dist\\compiler-core.cjs.js:6363:21) at transformModel (C:\\home\\PurchaseReview\\repo\\frontend\\node_modules\\@vue\\compiler-dom\\dist\\compiler-dom.cjs.js:219:35) at buildProps (C:\\home\\PurchaseReview\\repo\\frontend\\node_modules\\@vue\\compiler-core\\dist\\compiler-core.cjs.js:5854:48) at Array.postTransformElement (C:\\home\\PurchaseReview\\repo\\frontend\\node_modules\\@vue\\compiler-core\\dist\\compiler-core.cjs.js:5476:32) at traverseNode (C:\\home\\PurchaseReview\\repo\\frontend\\node_modules\\@vue\\compiler-core\\dist\\compiler-core.cjs.js:3591:15) at traverseChildren (C:\\home\\PurchaseReview\\repo\\frontend\\node_modules\\@vue\\compiler-core\\dist\\compiler-core.cjs.js:3542:5) at traverseNode (C:\\home\\PurchaseReview\\repo\\frontend\\node_modules\\@vue\\compiler-core\\dist\\compiler-core.cjs.js:3585:7) at transform (C:\\home\\PurchaseReview\\repo\\frontend\\node_modules\\@vue\\compiler-core\\dist\\compiler-core.cjs.js:3478:3) at Object.baseCompile (C:\\home\\PurchaseReview\\repo\\frontend\\node_modules\\@vue\\compiler-core\\dist\\compiler-core.cjs.js:6656:3 Click outside, press Esc key, or fix the code to dismiss. You can also disable this overlay by setting"
- code: server.hmr.overlay
- text: to
- code: "false"
- text: in
- code: vite.config.ts.
```