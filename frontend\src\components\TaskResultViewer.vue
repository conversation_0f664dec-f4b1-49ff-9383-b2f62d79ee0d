<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="dialogTitle"
    width="90vw"
    :before-close="handleClose"
    class="task-result-dialog"
  >
    <div v-loading="loading" class="result-viewer-content">
      <!-- 結果不存在 -->
      <div v-if="!loading && !resultData" class="no-result">
        <el-empty description="暫無分析結果">
          <el-button type="primary" @click="handleRefresh">重新載入</el-button>
        </el-empty>
      </div>

      <!-- 結果內容 -->
      <div v-else-if="resultData" class="result-content">
        <!-- 結果摘要 -->
        <el-card class="result-summary" shadow="never">
          <template #header>
            <div class="summary-header">
              <h3>{{ resultData.title || '分析結果' }}</h3>
              <div class="result-meta">
                <el-tag v-if="resultData.confidence_score" type="success">
                  信心度: {{ Math.round(resultData.confidence_score) }}%
                </el-tag>
                <el-tag v-if="resultData.status" :type="getStatusType(resultData.status)">
                  {{ getStatusText(resultData.status) }}
                </el-tag>
              </div>
            </div>
          </template>

          <div v-if="resultData.summary" class="summary-content">
            <p>{{ resultData.summary }}</p>
          </div>
          <div v-else-if="resultData.description" class="summary-content">
            <p>{{ resultData.description }}</p>
          </div>
        </el-card>

        <!-- 詳細內容 -->
        <el-tabs v-model="activeTab" type="card" class="result-tabs">
          <!-- 主要內容 -->
          <el-tab-pane label="分析內容" name="content">
            <div class="content-section">
              <div v-if="resultData.content" class="content-text">
                <div v-html="formatContent(resultData.content)"></div>
              </div>
              <div v-else-if="resultData.content_data" class="content-data">
                <pre>{{ JSON.stringify(resultData.content_data, null, 2) }}</pre>
              </div>
              <div v-else class="no-content">
                <el-empty description="暫無詳細內容" />
              </div>
            </div>
          </el-tab-pane>

          <!-- 關鍵發現 -->
          <el-tab-pane
            v-if="resultData.key_findings && resultData.key_findings.length > 0"
            label="關鍵發現"
            name="findings"
          >
            <div class="findings-section">
              <el-card
                v-for="(finding, index) in resultData.key_findings"
                :key="index"
                class="finding-card"
                shadow="hover"
              >
                <h4 v-if="finding.title">{{ finding.title }}</h4>
                <p v-if="finding.description">{{ finding.description }}</p>
                <div v-if="finding.details" class="finding-details">
                  <div v-html="formatContent(finding.details)"></div>
                </div>
                <div v-if="finding.confidence" class="finding-confidence">
                  <el-tag size="small" :type="getConfidenceType(finding.confidence)">
                    信心度: {{ Math.round(finding.confidence) }}%
                  </el-tag>
                </div>
              </el-card>
            </div>
          </el-tab-pane>

          <!-- 建議事項 -->
          <el-tab-pane
            v-if="resultData.recommendations && resultData.recommendations.length > 0"
            label="建議事項"
            name="recommendations"
          >
            <div class="recommendations-section">
              <el-card
                v-for="(rec, index) in resultData.recommendations"
                :key="index"
                class="recommendation-card"
                shadow="hover"
              >
                <h4 v-if="rec.title">{{ rec.title }}</h4>
                <p v-if="rec.description">{{ rec.description }}</p>
                <div v-if="rec.details" class="recommendation-details">
                  <div v-html="formatContent(rec.details)"></div>
                </div>
                <div v-if="rec.priority" class="recommendation-priority">
                  <el-tag size="small" :type="getPriorityType(rec.priority)">
                    優先級: {{ rec.priority }}
                  </el-tag>
                </div>
              </el-card>
            </div>
          </el-tab-pane>

          <!-- 元數據 -->
          <el-tab-pane label="詳細信息" name="metadata">
            <div class="metadata-section">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="結果ID">{{ resultData.result_id }}</el-descriptions-item>
                <el-descriptions-item label="任務ID">{{ resultData.task_id || '無' }}</el-descriptions-item>
                <el-descriptions-item label="購案ID">{{ resultData.purchase_id }}</el-descriptions-item>
                <el-descriptions-item label="結果類型">{{ resultData.result_type }}</el-descriptions-item>
                <el-descriptions-item label="創建時間">{{ formatTime(resultData.created_time) }}</el-descriptions-item>
                <el-descriptions-item label="更新時間">{{ formatTime(resultData.updated_time) }}</el-descriptions-item>
                <el-descriptions-item label="字數統計">{{ resultData.word_count || 0 }}</el-descriptions-item>
                <el-descriptions-item label="查看次數">{{ resultData.view_count || 0 }}</el-descriptions-item>
                <el-descriptions-item v-if="resultData.quality_score" label="質量評分">
                  {{ Math.round(resultData.quality_score) }}%
                </el-descriptions-item>
                <el-descriptions-item v-if="resultData.relevance_score" label="相關性評分">
                  {{ Math.round(resultData.relevance_score) }}%
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">關閉</el-button>
        <el-button v-if="resultData" type="primary" @click="handleExport">導出結果</el-button>
        <el-button v-if="!loading" @click="handleRefresh">重新載入</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { analysisResultsAPI } from '@/services/api'

// Props
interface Props {
  visible: boolean
  taskId?: string
  resultId?: string
  taskTitle?: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  taskId: '',
  resultId: '',
  taskTitle: '任務結果'
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  close: []
}>()

// 響應式數據
const loading = ref(false)
const resultData = ref<any>(null)
const activeTab = ref('content')

// 計算屬性
const dialogTitle = computed(() => {
  if (resultData.value?.title) {
    return `${props.taskTitle} - ${resultData.value.title}`
  }
  return `${props.taskTitle} - 分析結果`
})

// 方法
const loadResult = async () => {
  if (!props.taskId && !props.resultId) {
    console.warn('⚠️ 沒有提供任務ID或結果ID')
    return
  }

  loading.value = true
  try {
    let response

    if (props.resultId) {
      // 直接通過結果ID獲取
      response = await analysisResultsAPI.getAnalysisResult(props.resultId, true)
    } else if (props.taskId) {
      // 通過任務ID獲取結果
      response = await analysisResultsAPI.getTaskResult(props.taskId)
    }

    if (response?.data) {
      resultData.value = response.data
      console.log('✅ 任務結果載入成功:', resultData.value)
    } else {
      console.warn('⚠️ 未找到分析結果')
      resultData.value = null
    }
  } catch (error: any) {
    console.error('❌ 載入任務結果失敗:', error)
    ElMessage.error(`載入結果失敗: ${error.response?.data?.detail || error.message}`)
    resultData.value = null
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

const handleRefresh = () => {
  loadResult()
}

const handleExport = async () => {
  if (!resultData.value?.result_id) {
    ElMessage.warning('無法導出：缺少結果ID')
    return
  }

  try {
    const response = await analysisResultsAPI.exportAnalysisResult(resultData.value.result_id, 'json', true)

    // 創建下載鏈接
    const blob = new Blob([JSON.stringify(response.data, null, 2)], { type: 'application/json' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `analysis_result_${resultData.value.result_id}.json`
    link.click()
    window.URL.revokeObjectURL(url)

    ElMessage.success('結果導出成功')
  } catch (error: any) {
    console.error('導出失敗:', error)
    ElMessage.error(`導出失敗: ${error.response?.data?.detail || error.message}`)
  }
}

// 格式化內容
const formatContent = (content: string): string => {
  if (!content) return ''

  // 簡單的HTML格式化
  return content
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
}

// 格式化時間
const formatTime = (timeStr: string): string => {
  if (!timeStr) return '無'
  try {
    return new Date(timeStr).toLocaleString('zh-TW')
  } catch {
    return timeStr
  }
}

// 獲取狀態類型
const getStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    'draft': 'info',
    'published': 'success',
    'archived': 'warning',
    'deleted': 'danger'
  }
  return statusMap[status] || 'info'
}

// 獲取狀態文字
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'draft': '草稿',
    'published': '已發布',
    'archived': '已歸檔',
    'deleted': '已刪除'
  }
  return statusMap[status] || status
}

// 獲取信心度類型
const getConfidenceType = (confidence: number): string => {
  if (confidence >= 80) return 'success'
  if (confidence >= 60) return 'warning'
  return 'danger'
}

// 獲取優先級類型
const getPriorityType = (priority: string): string => {
  const priorityMap: Record<string, string> = {
    'high': 'danger',
    'medium': 'warning',
    'low': 'info'
  }
  return priorityMap[priority] || 'info'
}

// 監聽visible變化
watch(() => props.visible, (newVisible) => {
  if (newVisible && (props.taskId || props.resultId)) {
    loadResult()
  }
})
</script>

<style scoped>
.task-result-dialog {
  --el-dialog-margin-top: 5vh;
}

.result-viewer-content {
  max-height: 70vh;
  overflow-y: auto;
}

.no-result {
  text-align: center;
  padding: 40px 20px;
}

.result-summary {
  margin-bottom: 20px;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-header h3 {
  margin: 0;
  color: #303133;
}

.result-meta {
  display: flex;
  gap: 8px;
}

.summary-content {
  color: #606266;
  line-height: 1.6;
}

.result-tabs {
  margin-top: 20px;
}

.content-section,
.findings-section,
.recommendations-section,
.metadata-section {
  padding: 20px 0;
}

.content-text,
.content-data {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 6px;
  line-height: 1.6;
}

.content-data pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.finding-card,
.recommendation-card {
  margin-bottom: 16px;
}

.finding-card h4,
.recommendation-card h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.finding-details,
.recommendation-details {
  margin: 12px 0;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  line-height: 1.6;
}

.finding-confidence,
.recommendation-priority {
  margin-top: 12px;
}

.no-content {
  text-align: center;
  padding: 40px 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .task-result-dialog {
    --el-dialog-margin-top: 2vh;
  }

  .summary-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .result-meta {
    align-self: stretch;
  }
}
</style>
