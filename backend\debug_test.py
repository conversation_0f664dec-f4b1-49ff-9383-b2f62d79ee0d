"""
調試測試腳本 - 直接測試文件上傳邏輯
"""

import sys
import os
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 設置環境變量
os.environ["UPLOAD_DIR"] = "./uploads"
os.environ["TEMP_DIR"] = "./temp"
os.environ["MAX_FILE_SIZE"] = "52428800"
os.environ["ALLOWED_FILE_TYPES"] = '[".pdf", ".doc", ".docx"]'

def test_file_status_enum():
    """測試 FileStatus 枚舉"""
    print("=== 測試 FileStatus 枚舉 ===")
    
    try:
        from app.schemas.upload import FileStatus
        print(f"FileStatus.UPLOADED = {FileStatus.UPLOADED}")
        print(f"FileStatus.UPLOADED.value = {FileStatus.UPLOADED.value}")
        print(f"類型: {type(FileStatus.UPLOADED)}")
        print(f"值類型: {type(FileStatus.UPLOADED.value)}")
        print("✅ FileStatus 枚舉測試通過")
    except Exception as e:
        print(f"❌ FileStatus 枚舉測試失敗: {e}")
        import traceback
        traceback.print_exc()

def test_file_record_creation():
    """測試 FileRecord 創建"""
    print("\n=== 測試 FileRecord 創建 ===")
    
    try:
        from app.models.file import FileRecord
        from app.schemas.upload import FileStatus
        from datetime import datetime
        
        # 測試創建 FileRecord
        file_record = FileRecord(
            file_id="test-123",
            original_filename="test.pdf",
            stored_filename="test-123.pdf",
            file_size=1024,
            file_path="/uploads/test-123.pdf",
            parse_method="text",
            status=FileStatus.UPLOADED.value,  # 使用 .value
            upload_time=datetime.utcnow()
        )
        
        print(f"FileRecord 創建成功")
        print(f"status = {file_record.status}")
        print(f"status 類型 = {type(file_record.status)}")
        print("✅ FileRecord 創建測試通過")
        
    except Exception as e:
        print(f"❌ FileRecord 創建測試失敗: {e}")
        import traceback
        traceback.print_exc()

def test_enhanced_file_service():
    """測試 EnhancedFileService"""
    print("\n=== 測試 EnhancedFileService ===")
    
    try:
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker
        from app.models.file import Base
        from app.services.enhanced_file_service import EnhancedFileService
        
        # 創建內存數據庫
        engine = create_engine("sqlite:///:memory:", echo=False)
        Base.metadata.create_all(bind=engine)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        db = SessionLocal()
        
        # 創建服務實例
        service = EnhancedFileService(db)
        print("✅ EnhancedFileService 創建成功")
        
        db.close()
        
    except Exception as e:
        print(f"❌ EnhancedFileService 測試失敗: {e}")
        import traceback
        traceback.print_exc()

def test_file_utils():
    """測試文件工具"""
    print("\n=== 測試文件工具 ===")
    
    try:
        from app.utils.file_utils import file_manager, file_validator
        print("✅ 文件工具導入成功")
        
        # 測試文件管理器
        file_id = file_manager.generate_file_id()
        print(f"生成文件ID: {file_id}")
        
        stored_filename = file_manager.generate_stored_filename("test.pdf", file_id)
        print(f"生成存儲文件名: {stored_filename}")
        
        print("✅ 文件工具測試通過")
        
    except Exception as e:
        print(f"❌ 文件工具測試失敗: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主測試函數"""
    print("開始調試測試...")
    
    # 創建必要的目錄
    Path("./uploads").mkdir(exist_ok=True)
    Path("./temp").mkdir(exist_ok=True)
    
    test_file_status_enum()
    test_file_record_creation()
    test_enhanced_file_service()
    test_file_utils()
    
    print("\n調試測試完成!")

if __name__ == "__main__":
    main()
