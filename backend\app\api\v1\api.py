"""
API v1 路由配置
"""

from fastapi import APIRouter

from app.api.v1.endpoints import (
    health,
    upload,
    parse,
    # knowledge,  # 暫時禁用
    training,
    stats,
    purchases,
    files,
    analysis_tasks,
    # rag_analysis,  # 暫時禁用
    analysis_results,
    task_management,
    websocket,
    general_rag,
    rag_database_management,
    sqlite_management,
    sqlite_monitoring,
    rag_mode_switch
)
from app.api.v1 import prompt_management

# 創建 API 路由器
api_router = APIRouter()

# 包含各個端點路由
api_router.include_router(
    health.router,
    prefix="/health",
    tags=["健康檢查"]
)

api_router.include_router(
    upload.router,
    prefix="/upload",
    tags=["文件上傳"]
)

api_router.include_router(
    parse.router,
    prefix="/parse",
    tags=["PDF解析"]
)

# 暫時禁用知識庫功能，等待修復
# api_router.include_router(
#     knowledge.router,
#     prefix="/knowledge",
#     tags=["知識庫管理"]
# )

api_router.include_router(
    training.router,
    prefix="/training",
    tags=["GraphRAG訓練"]
)

api_router.include_router(
    stats.router,
    prefix="/stats",
    tags=["系統統計"]
)

# 購案管理
api_router.include_router(
    purchases.router,
    prefix="/purchases",
    tags=["購案管理"]
)

# 文件管理
api_router.include_router(
    files.router,
    prefix="/files",
    tags=["文件管理"]
)

# 分析任務
api_router.include_router(
    analysis_tasks.router,
    prefix="/tasks",
    tags=["分析任務"]
)

# RAG分析 - 暫時禁用
# api_router.include_router(
#     rag_analysis.router,
#     prefix="/rag",
#     tags=["RAG分析"]
# )

# 分析結果
api_router.include_router(
    analysis_results.router,
    prefix="/results",
    tags=["分析結果"]
)

# 任務管理
api_router.include_router(
    task_management.router,
    prefix="/task-management",
    tags=["任務管理"]
)

# WebSocket
api_router.include_router(
    websocket.router,
    prefix="/ws",
    tags=["實時通信"]
)

# 通用RAG
api_router.include_router(
    general_rag.router,
    prefix="/general-rag",
    tags=["通用RAG服務"]
)

# RAG資料庫管理
api_router.include_router(
    rag_database_management.router,
    prefix="/rag-db-management",
    tags=["RAG資料庫管理"]
)

# SQLite資料庫管理
api_router.include_router(
    sqlite_management.router,
    prefix="/sqlite-management",
    tags=["SQLite資料庫管理"]
)

# SQLite資料庫監控
api_router.include_router(
    sqlite_monitoring.router,
    prefix="/sqlite-monitoring",
    tags=["SQLite資料庫監控"]
)

# RAG模式切換
api_router.include_router(
    rag_mode_switch.router,
    prefix="/rag-mode-switch",
    tags=["RAG模式切換"]
)

# Prompt 管理
api_router.include_router(
    prompt_management.router,
    prefix="/prompt-management",
    tags=["Prompt管理"]
)
