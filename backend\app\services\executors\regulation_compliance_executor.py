"""
法規比對執行器
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime

try:
    from langchain_community.llms import Ollama    
except ImportError:
    try:
        from langchain_ollama import OllamaLLM as Ollama
    except ImportError:
        Ollama = None

from langchain.callbacks.base import BaseCallbackHandler

from app.models.analysis_task import AnalysisTask
from .base_executor import PurchaseReviewExecutor
from app.core.config import settings

logger = logging.getLogger(__name__)


class ProgressCallbackHandler(BaseCallbackHandler):
    """進度追蹤回調處理器"""

    def __init__(self, executor, task: AnalysisTask):
        self.executor = executor
        self.task = task
        self.step_count = 0
        self.total_steps = 10  # 預估總步驟數
        logger.info(f"初始化進度追蹤回調處理器 - 任務ID: {task.task_id}, 預估步驟數: {self.total_steps}")

    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs) -> None:
        """LLM 開始時的回調"""
        self.step_count += 1
        progress = min(20 + (self.step_count * 60 // self.total_steps), 90)
        progress_message = f"AI分析中 (步驟 {self.step_count}/{self.total_steps})"

        logger.info(f"LLM 開始處理 - 任務ID: {self.task.task_id}, 步驟: {self.step_count}/{self.total_steps}, 進度: {progress}%")
        logger.debug(f"LLM 序列化信息: {serialized}")
        logger.debug(f"提示數量: {len(prompts)}, 第一個提示長度: {len(prompts[0]) if prompts else 0}")

        self.executor.update_progress(self.task, progress, progress_message)

    def on_llm_end(self, response, **kwargs) -> None:
        """LLM 結束時的回調"""
        response_length = len(str(response)) if response else 0
        logger.info(f"LLM 回應完成 - 任務ID: {self.task.task_id}, 步驟: {self.step_count}, 回應長度: {response_length} 字符")

        if kwargs:
            logger.debug(f"LLM 結束額外參數: {kwargs}")

        # 記錄回應的前100個字符用於調試
        if response and logger.isEnabledFor(logging.DEBUG):
            response_preview = str(response)[:100] + "..." if len(str(response)) > 100 else str(response)
            logger.debug(f"LLM 回應預覽: {response_preview}")

    def on_llm_error(self, error: Exception, **kwargs) -> None:
        """LLM 錯誤時的回調"""
        logger.error(f"LLM 處理錯誤 - 任務ID: {self.task.task_id}, 步驟: {self.step_count}, 錯誤: {error}")
        if kwargs:
            logger.debug(f"LLM 錯誤額外參數: {kwargs}")


class RegulationComplianceExecutor(PurchaseReviewExecutor):
    """法規比對執行器"""

    def __init__(self, db):
        super().__init__(db)
        self.ollama_client = None
        self._init_ollama_client()

    def _init_ollama_client(self):
        """初始化 Ollama 客戶端"""
        try:
            if Ollama is None:
                logger.warning("Ollama 類別不可用，請安裝 langchain-ollama")
                self.ollama_client = None
                return

            self.ollama_client = Ollama(
                base_url=settings.OLLAMA_BASE_URL,
                model=settings.OLLAMA_MODEL,
                timeout=settings.OLLAMA_TIMEOUT,
                temperature=settings.OLLAMA_TEMPERATURE
            )
            logger.info(f"Ollama 客戶端初始化成功，模型: {settings.OLLAMA_MODEL}")
        except Exception as e:
            logger.error(f"Ollama 客戶端初始化失敗: {e}")
            self.ollama_client = None

    async def execute(self, task: AnalysisTask) -> Dict[str, Any]:
        """執行法規比對審查"""
        try:
            # 設定當前任務，讓輔助方法可以訪問
            self.current_task = task

            logger.info(f"開始執行法規比對任務 - 任務ID: {task.task_id}, 購案ID: {task.purchase_id}")
            self.update_progress(task, 5, "初始化法規比對分析")

            # 檢查 Ollama 客戶端是否可用
            if not self.ollama_client:
                logger.warning(f"Ollama 客戶端不可用，切換到基本分析模式 - 任務ID: {task.task_id}")
                return await self._basic_compliance_analysis(task)

            logger.info(f"使用 AI 模型進行法規比對分析 - 模型: {settings.OLLAMA_MODEL}, 任務ID: {task.task_id}")

            # 1. 提取購案文件內容
            logger.info(f"步驟 1/4: 開始提取購案文件內容 - 任務ID: {task.task_id}")
            self.update_progress(task, 10, "提取購案文件內容")
            document_content = await self._extract_document_content(task)
            if not document_content:
                raise Exception("文件內容提取失敗")
            logger.info(f"步驟 1/4: 文件內容提取完成，內容長度: {len(document_content)} 字符 - 任務ID: {task.task_id}")

            # 2. 分析購案條款
            logger.info(f"步驟 2/4: 開始 AI 分析購案條款 - 任務ID: {task.task_id}")
            self.update_progress(task, 20, "開始AI分析購案條款")
            clauses_analysis = await self._analyze_purchase_clauses(document_content)
            logger.info(f"步驟 2/4: 購案條款分析完成，識別關鍵條款: {len(clauses_analysis.get('key_clauses', []))} 個 - 任務ID: {task.task_id}")

            # 持久化條款分析結果
            logger.info(f"保存條款分析中間結果 - 任務ID: {task.task_id}")
            await self._save_intermediate_result(task, "clauses_analysis", clauses_analysis)

            # 3. 法規比對
            logger.info(f"步驟 3/4: 開始執行法規比對檢查 - 任務ID: {task.task_id}")
            self.update_progress(task, 60, "執行法規比對")
            compliance_check = await self._perform_regulation_compliance(clauses_analysis)
            compliance_score = compliance_check.get("compliance_score", 0)
            violations_count = len(compliance_check.get("violations", []))
            logger.info(f"步驟 3/4: 法規比對檢查完成，符合度分數: {compliance_score}%, 違規項目: {violations_count} 個 - 任務ID: {task.task_id}")

            # 持久化法規比對結果
            logger.info(f"保存法規比對檢查中間結果 - 任務ID: {task.task_id}")
            await self._save_intermediate_result(task, "compliance_check", compliance_check)

            # 4. 生成報告
            logger.info(f"步驟 4/4: 開始生成法規比對報告 - 任務ID: {task.task_id}")
            self.update_progress(task, 90, "生成法規比對報告")
            final_report = await self._generate_compliance_report(compliance_check)
            logger.info(f"步驟 4/4: 法規比對報告生成完成 - 任務ID: {task.task_id}")

            # 持久化最終報告
            logger.info(f"保存最終報告中間結果 - 任務ID: {task.task_id}")
            await self._save_intermediate_result(task, "final_report", final_report)

            self.update_progress(task, 100, "法規比對分析完成")
            logger.info(f"法規比對任務執行完成 - 任務ID: {task.task_id}, 最終符合度分數: {final_report.get('compliance_score', 0)}%")

            # 構建完整的結果數據，包含所有中間步驟
            complete_result = {
                "status": "completed",
                "result": "法規比對完成",
                "compliance_score": final_report.get("compliance_score", 0),
                "violations": final_report.get("violations", []),
                "recommendations": final_report.get("recommendations", []),
                "detailed_analysis": final_report.get("detailed_analysis", {}),
                "ai_model_used": settings.OLLAMA_MODEL,
                "analysis_timestamp": datetime.now().isoformat(),

                # 包含所有中間步驟結果
                "intermediate_results": {
                    "document_content": document_content[:500] + "..." if len(document_content) > 500 else document_content,
                    "clauses_analysis": clauses_analysis,
                    "compliance_check": compliance_check,
                    "final_report": final_report
                }
            }

            logger.info(f"法規比對任務結果構建完成 - 任務ID: {task.task_id}")
            return complete_result

        except Exception as e:
            logger.error(f"法規比對執行失敗 - 任務ID: {task.task_id}, 錯誤: {e}", exc_info=True)
            self.update_progress(task, 100, f"執行失敗: {str(e)}")
            return {
                "status": "failed",
                "error": str(e),
                "error_type": type(e).__name__
            }

    async def _extract_document_content(self, task: AnalysisTask) -> str:
        """提取購案文件內容"""
        try:
            logger.info(f"開始提取文件內容 - 任務ID: {task.task_id}, 購案ID: {task.purchase_id}")

            # 從購案相關的解析結果中提取內容
            from app.services.analysis_task_service import AnalysisTaskService
            from app.models.analysis_task import TaskType

            task_service = AnalysisTaskService(self.db)

            # 查找同一購案的文字解析任務結果
            logger.info(f"查找購案 {task.purchase_id} 的文字解析任務")
            parse_tasks = task_service.get_tasks_by_purchase_and_type(
                task.purchase_id,
                TaskType.PDF_PARSE
            )
            logger.info(f"找到 {len(parse_tasks)} 個文字解析任務")

            document_content = ""
            processed_tasks = 0

            for parse_task in parse_tasks:
                logger.debug(f"檢查解析任務 {parse_task.task_id} - 狀態: {parse_task.status.value}")

                if (parse_task.status.value == "completed" and
                    parse_task.config and
                    parse_task.config.get("step") == "text_extraction" and
                    parse_task.result_data):

                    # 提取解析結果中的文字內容
                    result_data = parse_task.result_data
                    if result_data:
                        document_content += result_data.get("text_content")
                        processed_tasks += 1
                        logger.info(f"從解析任務 {parse_task.task_id} 提取到 {len(result_data)} 字符的內容")

            logger.info(f"文件內容提取完成 - 處理了 {processed_tasks} 個任務，總內容長度: {len(document_content)} 字符")

            # 如果沒有找到解析內容，使用模擬數據
            if not document_content.strip():
                logger.warning(f"未找到購案 {task.purchase_id} 的解析內容")                
                raise Exception(f"解析內容失敗：未找到購案 {task.purchase_id} 的解析內容") 

            return document_content           

        except Exception as e:
            logger.error(f"提取文件內容失敗 - 任務ID: {task.task_id}, 錯誤: {e}", exc_info=True)
            return None

    async def _analyze_purchase_clauses(self, content: str) -> Dict[str, Any]:
        """使用 AI 分析購案條款"""
        try:
            logger.info(f"開始 AI 條款分析，內容長度: {len(content)} 字符")

            # 檢查是否有自訂 prompt
            custom_prompt = self._get_custom_prompt("clauses_analysis")

            if custom_prompt:
                # 使用自訂 prompt
                prompt = custom_prompt.format(content=content)
                logger.info("使用自訂 prompt 進行條款分析")
            else:
                # 使用預設 prompt
                prompt = self._get_default_clauses_analysis_prompt(content)
                logger.info("使用預設 prompt 進行條款分析")

            logger.info(f"發送 AI 分析請求到 Ollama，模型: {settings.OLLAMA_MODEL}")
            logger.debug(f"AI 分析提示長度: {len(prompt)} 字符")

            response = await asyncio.to_thread(
                self.ollama_client.invoke,
                prompt
            )

            logger.info(f"AI 條款分析回應完成，回應長度: {len(response)} 字符")

            # 儲存完整的原始回覆
            result = {
                "raw_response": response,
                "analysis_timestamp": datetime.now().isoformat(),
                "prompt_type": "custom" if custom_prompt else "default",
                "model_used": settings.OLLAMA_MODEL
            }

            # 檢查是否要求 JSON 格式
            force_json = self._should_force_json_format()

            if force_json:
                # 嘗試解析 JSON 回應
                try:
                    import json
                    parsed_response = json.loads(response)
                    key_clauses_count = len(parsed_response.get("key_clauses", []))
                    issues_count = len(parsed_response.get("potential_issues", []))
                    areas_count = len(parsed_response.get("compliance_areas", []))

                    logger.info(f"AI 條款分析結果解析成功 - 關鍵條款: {key_clauses_count} 個, 潛在問題: {issues_count} 個, 法規領域: {areas_count} 個")

                    # 合併 JSON 解析結果和原始回覆
                    result.update(parsed_response)
                    result["json_parsed"] = True
                    return result

                except json.JSONDecodeError as json_error:
                    logger.warning(f"AI 回應 JSON 解析失敗: {json_error}，保留原始回覆並提供基本結構")
                    # 提供基本結構但保留原始回覆
                    result.update({
                        "key_clauses": self._extract_basic_clauses(response),
                        "potential_issues": ["需要進一步檢查法規符合性"],
                        "compliance_areas": ["政府採購法", "國防採購規定"],
                        "json_parsed": False,
                        "parse_error": str(json_error)
                    })
                    logger.info("JSON 解析失敗，使用基本結構並保留原始回覆")
                    return result
            else:
                # 不強制 JSON 格式，直接返回原始回覆
                result.update({
                    "analysis_content": response,
                    "format": "raw",
                    "json_parsed": False
                })
                logger.info("返回原始 AI 回覆，未進行 JSON 解析")
                return result

        except Exception as e:
            logger.error(f"AI 條款分析失敗: {e}", exc_info=True)
            return {
                "raw_response": "",
                "key_clauses": [],
                "potential_issues": [f"分析失敗: {str(e)}"],
                "compliance_areas": [],
                "error": str(e),
                "json_parsed": False
            }

    async def _perform_regulation_compliance(self, clauses_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """執行法規比對檢查"""
        try:
            compliance_areas = clauses_analysis.get("compliance_areas", [])
            key_clauses = clauses_analysis.get("key_clauses", [])
            potential_issues = clauses_analysis.get("potential_issues", [])

            logger.info(f"開始法規比對檢查 - 關鍵條款: {len(key_clauses)} 個, 法規領域: {len(compliance_areas)} 個, 潛在問題: {len(potential_issues)} 個")
            logger.debug(f"法規領域: {compliance_areas}")
            logger.debug(f"關鍵條款: {key_clauses}")

            # 檢查是否有自訂 prompt
            custom_prompt = self._get_custom_prompt("compliance_check")

            if custom_prompt:
                # 使用自訂 prompt
                prompt = custom_prompt.format(
                    key_clauses=key_clauses,
                    compliance_areas=compliance_areas,
                    potential_issues=potential_issues
                )
                logger.info("使用自訂 prompt 進行法規比對檢查")
            else:
                # 使用預設 prompt
                prompt = f"""
                基於以下條款分析結果，請進行詳細的法規比對檢查：

                關鍵條款：{key_clauses}
                需檢查的法規領域：{compliance_areas}
                潛在問題：{potential_issues}

                請檢查以下法規符合性：
                1. 政府採購法相關規定
                2. 國防部採購作業規定
                3. 預算法相關條文
                4. 契約條款標準範本
                5. 驗收作業規定

                請以 JSON 格式回應，包含：
                - compliance_score: 符合度分數 (0-100)
                - violations: 違規項目列表
                - warnings: 警告項目列表
                - recommendations: 改善建議
                - detailed_checks: 詳細檢查結果
                """
                logger.info("使用預設 prompt 進行法規比對檢查")

            logger.info(f"發送法規比對檢查請求到 AI，提示長度: {len(prompt)} 字符")

            response = await asyncio.to_thread(
                self.ollama_client.invoke,
                prompt
            )

            logger.info(f"法規比對檢查回應完成，回應長度: {len(response)} 字符")

            # 儲存完整的原始回覆
            result = {
                "raw_response": response,
                "analysis_timestamp": datetime.now().isoformat(),
                "prompt_type": "custom" if custom_prompt else "default",
                "model_used": settings.OLLAMA_MODEL
            }

            # 檢查是否要求 JSON 格式
            force_json = self._should_force_json_format()

            if force_json:
                try:
                    import json
                    parsed_response = json.loads(response)
                    compliance_score = parsed_response.get("compliance_score", 0)
                    violations_count = len(parsed_response.get("violations", []))
                    warnings_count = len(parsed_response.get("warnings", []))
                    recommendations_count = len(parsed_response.get("recommendations", []))

                    logger.info(f"法規比對檢查結果解析成功 - 符合度: {compliance_score}%, 違規: {violations_count} 個, 警告: {warnings_count} 個, 建議: {recommendations_count} 個")

                    # 合併 JSON 解析結果和原始回覆
                    result.update(parsed_response)
                    result["json_parsed"] = True
                    return result

                except json.JSONDecodeError as json_error:
                    logger.warning(f"法規比對檢查回應 JSON 解析失敗: {json_error}，保留原始回覆並提供基本結構")
                    result.update({
                        "compliance_score": 75,
                        "violations": [],
                        "warnings": ["需要人工審查"],
                        "recommendations": ["建議進行詳細法規檢查"],
                        "detailed_checks": {},
                        "json_parsed": False,
                        "parse_error": str(json_error)
                    })
                    logger.info("JSON 解析失敗，使用基本結構並保留原始回覆")
                    return result
            else:
                # 不強制 JSON 格式，直接返回原始回覆
                result.update({
                    "analysis_content": response,
                    "format": "raw",
                    "json_parsed": False
                })
                logger.info("返回原始 AI 回覆，未進行 JSON 解析")
                return result

        except Exception as e:
            logger.error(f"法規比對檢查失敗: {e}", exc_info=True)
            return {
                "raw_response": "",
                "compliance_score": 0,
                "violations": [f"檢查失敗: {str(e)}"],
                "warnings": [],
                "recommendations": ["請手動進行法規檢查"],
                "error": str(e),
                "json_parsed": False
            }

    async def _generate_compliance_report(self, compliance_check: Dict[str, Any]) -> Dict[str, Any]:
        """生成最終的法規比對報告"""
        try:
            compliance_score = compliance_check.get("compliance_score", 0)
            violations = compliance_check.get("violations", [])
            warnings = compliance_check.get("warnings", [])
            recommendations = compliance_check.get("recommendations", [])

            logger.info(f"開始生成法規比對報告 - 符合度: {compliance_score}%, 違規: {len(violations)} 個, 警告: {len(warnings)} 個, 建議: {len(recommendations)} 個")

            # 檢查是否有自訂 prompt
            custom_prompt = self._get_custom_prompt("final_report")

            if custom_prompt:
                # 使用自訂 prompt
                prompt = custom_prompt.format(
                    compliance_score=compliance_score,
                    violations=violations,
                    warnings=warnings,
                    recommendations=recommendations
                )
                logger.info("使用自訂 prompt 進行報告生成")
            else:
                # 使用預設 prompt
                prompt = f"""
                基於以下法規比對檢查結果，請生成一份完整的法規比對報告：

                符合度分數：{compliance_score}
                違規項目：{violations}
                警告項目：{warnings}
                改善建議：{recommendations}

                請生成一份專業的法規比對報告，包含：
                1. 執行摘要
                2. 主要發現
                3. 風險評估
                4. 改善建議
                5. 後續行動計畫

                請以 JSON 格式回應，包含：
                - executive_summary: 執行摘要
                - key_findings: 主要發現
                - risk_assessment: 風險評估
                - improvement_plan: 改善計畫
                - compliance_score: 最終符合度分數
                - violations: 違規項目
                - recommendations: 建議事項
                """
                logger.info("使用預設 prompt 進行報告生成")

            logger.info(f"發送報告生成請求到 AI，提示長度: {len(prompt)} 字符")

            response = await asyncio.to_thread(
                self.ollama_client.invoke,
                prompt
            )

            logger.info(f"報告生成回應完成，回應長度: {len(response)} 字符")

            # 儲存完整的原始回覆
            result = {
                "raw_response": response,
                "analysis_timestamp": datetime.now().isoformat(),
                "prompt_type": "custom" if custom_prompt else "default",
                "model_used": settings.OLLAMA_MODEL
            }

            # 檢查是否要求 JSON 格式
            force_json = self._should_force_json_format()

            if force_json:
                try:
                    import json
                    parsed_result = json.loads(response)
                    # 確保包含必要的欄位
                    parsed_result.setdefault("compliance_score", compliance_score)
                    parsed_result.setdefault("violations", violations)
                    parsed_result.setdefault("recommendations", recommendations)

                    logger.info(f"法規比對報告生成成功 - 最終符合度: {parsed_result.get('compliance_score', 0)}%")
                    logger.debug(f"報告包含欄位: {list(parsed_result.keys())}")

                    # 合併 JSON 解析結果和原始回覆
                    result.update(parsed_result)
                    result["json_parsed"] = True
                    return result

                except json.JSONDecodeError as json_error:
                    logger.warning(f"報告生成回應 JSON 解析失敗: {json_error}，保留原始回覆並提供基本結構")
                    result.update({
                        "executive_summary": "法規比對分析已完成",
                        "key_findings": ["需要進一步人工審查"],
                        "risk_assessment": "中等風險",
                        "improvement_plan": ["建議進行詳細法規檢查"],
                        "compliance_score": compliance_score,
                        "violations": violations,
                        "recommendations": recommendations,
                        "json_parsed": False,
                        "parse_error": str(json_error)
                    })
                    logger.info("JSON 解析失敗，使用基本結構並保留原始回覆")
                    return result
            else:
                # 不強制 JSON 格式，直接返回原始回覆
                result.update({
                    "analysis_content": response,
                    "format": "raw",
                    "compliance_score": compliance_score,
                    "violations": violations,
                    "recommendations": recommendations,
                    "json_parsed": False
                })
                logger.info("返回原始 AI 回覆，未進行 JSON 解析")
                return result

        except Exception as e:
            logger.error(f"生成報告失敗: {e}", exc_info=True)
            return {
                "raw_response": "",
                "executive_summary": f"報告生成失敗: {str(e)}",
                "key_findings": [],
                "risk_assessment": "無法評估",
                "improvement_plan": [],
                "compliance_score": compliance_check.get("compliance_score", 0),
                "violations": compliance_check.get("violations", []),
                "recommendations": ["請手動生成報告"],
                "error": str(e),
                "json_parsed": False
            }

    async def _basic_compliance_analysis(self, task: AnalysisTask) -> Dict[str, Any]:
        """基本法規比對分析（當 AI 不可用時）"""
        try:
            logger.info(f"開始基本法規比對分析 - 任務ID: {task.task_id} (AI 不可用)")
            self.update_progress(task, 20, "執行基本法規檢查")

            # 基本的規則檢查
            basic_checks = [
                "採購程序檢查",
                "預算合理性檢查",
                "契約條款檢查",
                "驗收標準檢查"
            ]

            violations = []
            recommendations = [
                "建議啟用 AI 模型進行詳細分析",
                "請人工審查關鍵條款",
                "確認法規最新版本"
            ]

            logger.info(f"基本法規檢查項目: {len(basic_checks)} 個")
            self.update_progress(task, 60, "分析基本合規項目")

            # 模擬基本檢查過程
            for i, check in enumerate(basic_checks):
                progress = 60 + (i + 1) * 8
                logger.info(f"執行基本檢查 ({i+1}/{len(basic_checks)}): {check}")
                self.update_progress(task, progress, f"檢查: {check}")
                await asyncio.sleep(0.1)  # 模擬處理時間

            self.update_progress(task, 100, "基本法規檢查完成")
            logger.info(f"基本法規比對分析完成 - 任務ID: {task.task_id}, 保守符合度分數: 70%")

            return {
                "status": "completed",
                "result": "基本法規比對完成（建議使用 AI 模型進行詳細分析）",
                "compliance_score": 70,  # 保守分數
                "violations": violations,
                "recommendations": recommendations,
                "analysis_mode": "basic",
                "note": "此為基本分析模式，建議配置 Ollama 進行詳細 AI 分析"
            }

        except Exception as e:
            logger.error(f"基本法規分析失敗 - 任務ID: {task.task_id}, 錯誤: {e}", exc_info=True)
            return {
                "status": "failed",
                "error": str(e),
                "analysis_mode": "basic"
            }

    async def _save_intermediate_result(self, task: AnalysisTask, step_name: str, result_data: Dict[str, Any]):
        """保存中間步驟結果到資料庫"""
        try:
            from app.models.analysis_result import ResultType
            from app.services.result_storage_service import ResultStorageService
            import json

            # 創建結果存儲服務
            storage_service = ResultStorageService(self.db)

            # 準備中間結果數據
            step_title_map = {
                "clauses_analysis": "購案條款分析",
                "compliance_check": "法規比對檢查",
                "final_report": "法規比對報告"
            }

            title = step_title_map.get(step_name, f"中間結果 - {step_name}")
            description = f"法規比對任務的中間步驟結果：{step_name}"

            # 構建內容數據
            content_data = {
                "step_name": step_name,
                "step_data": result_data,
                "task_id": task.task_id,
                "purchase_id": task.purchase_id,
                "analysis_timestamp": datetime.now().isoformat(),
                "step_order": self._get_step_order(step_name)
            }

            # 提取關鍵信息
            summary = self._generate_step_summary(step_name, result_data)
            key_findings = self._extract_key_findings(step_name, result_data)
            recommendations = self._extract_recommendations(step_name, result_data)

            # 保存到資料庫
            analysis_result = await storage_service.store_analysis_result(
                purchase_id=task.purchase_id,
                task_id=task.task_id,
                title=title,
                description=description,
                result_type=ResultType.INTERMEDIATE,  # 使用中間結果類型
                content_data={
                    "summary": summary,
                    "content": json.dumps(content_data, ensure_ascii=False),  # 序列化為JSON字符串
                    "key_findings": key_findings,
                    "recommendations": recommendations,
                    "confidence_score": self._calculate_step_confidence(step_name, result_data),
                    "analysis_model": settings.OLLAMA_MODEL,
                    "analysis_method": "ai_analysis"
                }
            )

            logger.info(f"已保存中間結果到資料庫: {step_name} (result_id: {analysis_result.result_id})")

            # 更新任務配置，記錄中間結果的資料庫ID
            if not task.config:
                task.config = {}

            if "intermediate_result_ids" not in task.config:
                task.config["intermediate_result_ids"] = {}

            task.config["intermediate_result_ids"][step_name] = analysis_result.result_id
            self.task_service.update_task_config(task.task_id, task.config)

        except Exception as e:
            logger.error(f"保存中間結果到資料庫失敗 {step_name}: {e}")
            # 降級到簡單保存
            await self._save_intermediate_result_simple(task, step_name, result_data)

    def _get_step_order(self, step_name: str) -> int:
        """獲取步驟順序"""
        step_orders = {
            "clauses_analysis": 1,
            "compliance_check": 2,
            "final_report": 3
        }
        return step_orders.get(step_name, 0)

    def _generate_step_summary(self, step_name: str, result_data: Dict[str, Any]) -> str:
        """生成步驟摘要"""
        try:
            if step_name == "clauses_analysis":
                key_clauses = result_data.get("key_clauses", [])
                return f"識別了 {len(key_clauses)} 個關鍵條款，包括：{', '.join(key_clauses[:3])}{'...' if len(key_clauses) > 3 else ''}"

            elif step_name == "compliance_check":
                compliance_score = result_data.get("compliance_score", 0)
                violations = result_data.get("violations", [])
                return f"法規符合度評分：{compliance_score}%，發現 {len(violations)} 個潛在違規項目"

            elif step_name == "final_report":
                summary = result_data.get("summary", "")
                return summary or "法規比對分析報告已完成"

            else:
                return f"中間步驟 {step_name} 已完成"

        except Exception as e:
            logger.error(f"生成步驟摘要失敗: {e}")
            return f"步驟 {step_name} 已完成"

    def _extract_key_findings(self, step_name: str, result_data: Dict[str, Any]) -> List[str]:
        """提取關鍵發現"""
        try:
            findings = []

            if step_name == "clauses_analysis":
                key_clauses = result_data.get("key_clauses", [])
                potential_issues = result_data.get("potential_issues", [])
                findings.extend([f"關鍵條款：{clause}" for clause in key_clauses[:5]])
                findings.extend([f"潛在問題：{issue}" for issue in potential_issues[:3]])

            elif step_name == "compliance_check":
                violations = result_data.get("violations", [])
                warnings = result_data.get("warnings", [])
                findings.extend([f"違規項目：{violation}" for violation in violations[:3]])
                findings.extend([f"警告事項：{warning}" for warning in warnings[:3]])

            elif step_name == "final_report":
                main_findings = result_data.get("main_findings", [])
                findings.extend(main_findings[:5])

            return findings

        except Exception as e:
            logger.error(f"提取關鍵發現失敗: {e}")
            return []

    def _extract_recommendations(self, step_name: str, result_data: Dict[str, Any]) -> List[str]:
        """提取建議事項"""
        try:
            recommendations = []

            if step_name == "clauses_analysis":
                compliance_areas = result_data.get("compliance_areas", [])
                recommendations.extend([f"建議檢查 {area} 相關法規" for area in compliance_areas[:3]])

            elif step_name == "compliance_check":
                direct_recommendations = result_data.get("recommendations", [])
                recommendations.extend(direct_recommendations[:5])

            elif step_name == "final_report":
                improvement_plan = result_data.get("improvement_plan", [])
                recommendations.extend(improvement_plan[:5])

            return recommendations

        except Exception as e:
            logger.error(f"提取建議事項失敗: {e}")
            return []

    def _calculate_step_confidence(self, step_name: str, result_data: Dict[str, Any]) -> float:
        """計算步驟信心度"""
        try:
            if step_name == "clauses_analysis":
                # 基於識別的條款數量和質量
                key_clauses = result_data.get("key_clauses", [])
                base_score = min(len(key_clauses) * 10, 70)  # 最多70分
                if result_data.get("ai_analysis_used"):
                    base_score += 20  # AI分析加分
                return min(base_score, 95)

            elif step_name == "compliance_check":
                # 基於檢查的完整性
                compliance_score = result_data.get("compliance_score", 0)
                return min(compliance_score * 0.8 + 20, 95)  # 轉換為信心度

            elif step_name == "final_report":
                # 基於報告的完整性
                if result_data.get("summary") and result_data.get("main_findings"):
                    return 90.0
                elif result_data.get("summary") or result_data.get("main_findings"):
                    return 70.0
                else:
                    return 50.0

            else:
                return 60.0  # 默認信心度

        except Exception as e:
            logger.error(f"計算步驟信心度失敗: {e}")
            return 50.0

    async def _save_intermediate_result_simple(self, task: AnalysisTask, step_name: str, result_data: Dict[str, Any]):
        """簡單的中間結果保存（降級方案）"""
        try:
            # 更新任務配置，添加中間結果
            if not task.config:
                task.config = {}

            if "intermediate_results" not in task.config:
                task.config["intermediate_results"] = {}

            # 保存中間結果到任務配置中
            task.config["intermediate_results"][step_name] = {
                "data": result_data,
                "timestamp": datetime.now().isoformat(),
                "step_order": self._get_step_order(step_name)
            }

            # 更新數據庫中的任務配置
            self.task_service.update_task_config(task.task_id, task.config)

            logger.info(f"已保存中間結果（簡單模式）: {step_name} for task {task.task_id}")

        except Exception as e:
            logger.error(f"簡單保存中間結果失敗 {step_name}: {e}")

    async def _save_intermediate_result_to_file(self, task: AnalysisTask, step_name: str, result_data: Dict[str, Any]):
        """保存中間結果到文件系統"""
        try:
            import json
            from pathlib import Path

            # 創建結果目錄
            results_dir = Path("./analysis_results") / task.purchase_id / task.task_id
            results_dir.mkdir(parents=True, exist_ok=True)

            # 保存中間結果文件
            result_file = results_dir / f"{step_name}.json"
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "step_name": step_name,
                    "task_id": task.task_id,
                    "purchase_id": task.purchase_id,
                    "timestamp": datetime.now().isoformat(),
                    "data": result_data
                }, f, ensure_ascii=False, indent=2)

            logger.info(f"中間結果已保存到文件: {result_file}")

        except Exception as e:
            logger.error(f"保存中間結果文件失敗 {step_name}: {e}")

    async def _load_intermediate_result(self, task: AnalysisTask, step_name: str) -> Optional[Dict[str, Any]]:
        """載入中間步驟結果"""
        try:
            # 首先嘗試從資料庫載入
            if (task.config and
                "intermediate_result_ids" in task.config and
                step_name in task.config["intermediate_result_ids"]):

                result_id = task.config["intermediate_result_ids"][step_name]

                from app.services.result_storage_service import ResultStorageService
                storage_service = ResultStorageService(self.db)

                analysis_result = await storage_service.get_analysis_result(result_id)
                if analysis_result and analysis_result.content:
                    try:
                        import json
                        content_data = json.loads(analysis_result.content)
                        step_data = content_data.get("step_data", {})
                        logger.info(f"從資料庫載入中間結果: {step_name} (result_id: {result_id})")
                        return step_data
                    except json.JSONDecodeError:
                        logger.warning(f"解析中間結果內容失敗: {result_id}")

            # 降級到從任務配置載入
            if (task.config and
                "intermediate_results" in task.config and
                step_name in task.config["intermediate_results"]):

                intermediate_data = task.config["intermediate_results"][step_name]
                logger.info(f"從任務配置載入中間結果: {step_name} for task {task.task_id}")
                return intermediate_data.get("data")

            logger.warning(f"未找到中間結果: {step_name} for task {task.task_id}")
            return None

        except Exception as e:
            logger.error(f"載入中間結果失敗 {step_name}: {e}")
            return None

    def _get_custom_prompt(self, step_name: str) -> Optional[str]:
        """獲取自訂 prompt"""
        try:
            # 從任務配置中獲取自訂 prompt
            if hasattr(self, 'current_task') and self.current_task and self.current_task.config:
                custom_prompts = self.current_task.config.get("custom_prompts", {})
                return custom_prompts.get(step_name)

            # 從設定檔獲取自訂 prompt
            custom_prompts = getattr(settings, 'CUSTOM_PROMPTS', {})
            return custom_prompts.get(step_name)

        except Exception as e:
            logger.error(f"獲取自訂 prompt 失敗 {step_name}: {e}")
            return None

    def _should_force_json_format(self) -> bool:
        """檢查是否應該強制 JSON 格式"""
        try:
            # 從任務配置檢查
            if hasattr(self, 'current_task') and self.current_task and self.current_task.config:
                return self.current_task.config.get("force_json_format", True)

            # 從設定檔檢查
            return getattr(settings, 'FORCE_JSON_FORMAT', True)

        except Exception as e:
            logger.error(f"檢查 JSON 格式設定失敗: {e}")
            return True  # 預設強制 JSON

    def _get_default_clauses_analysis_prompt(self, content: str) -> str:
        """獲取預設的條款分析 prompt"""
        return f"""
        購案內容：
        {content}

        ---
        你是購案法規審查專家及助理，以上為送審的採購計畫，請協助逐項比較上述採購計畫中是否涵蓋下列審查要項類同之文字，並依序處理下列事項:
        1.本案購案編號為何?
        2.請以下列表格呈現各審查要項內容、採購計畫內容及涵蓋狀況，每欄避免過寬請自動換行並合宜排版

        審查要項如下:
        審查要項(決標原則-最低標):本案採分項報價、總價決標；各分項報價金額不可超過公告預算，否則判定不合格。
        審查要項(決標原則-最有利標):依政府採購法第52條第1項第3款規定，經本案評選委員會就各評選項目評定序位第一，且經出席評選委員過半數之決定者為最有利標為得標廠商
        審查要項(陸製品限制):本案採購計畫清單品項(含保固)產品及其製造(作)所需原料及元件，不同意開放廠商或其分包廠商，以原產地或廠牌為大陸地區、香港、澳門或其人(居)民、法人、團體、其他機構於第三地區投資之法人、機構或團體之財物。

        輸出MarkDown格式範例如下:
        審查事項 | 審查結果 | 審查結果
        審查要項(決標原則-最低標) : 本案採分項報價、總價決標；各分項報價金額不可超過公告預算，否則判定不合格。 | 不通過
        採購清單內容 : 無相關條文或文字描述。

        審查要項(決標原則-最有利標) : 依政府採購法第52條第1項第3款規定，經本案評選委員會就各評選項目評定序位第一，且經出席評選委員過半數之決定者為最有利標為得標廠商。 | 通過
        採購清單內容 :

        報價及決標方式 :
        (1) 採序位法、價格納入評比方式辦理。
        (2) 最有利標經評選委員會過半數之決定者為最有利標。
        審查要項(陸製品限制) : 本案採購計畫清單品項(含保固)產品及其製造(作)所需原料及元件，不同意開放廠商或其分包廠商，以原產地或廠牌為大陸地區、香港、澳門或其人(居)民、法人、團體、其他機構於第三地區投資之法人、機構或團體之財物。 | 通過
        採購清單內容 : 本案採購計畫清單品項 (含保固)產品及其元件,不同意開放廠商或其分包廠商,以原產地或廠牌為大陸地區、香港、澳門或其人(居)民、法人、團體、其他機構於第三地區投資之法人、機構或團體之財物。


        請以 JSON 格式回應，包含：
        - key_clauses: 關鍵條款列表
        - potential_issues: 潛在問題
        - compliance_areas: 需要檢查的法規領域
        """

    def _extract_basic_clauses(self, response: str) -> List[str]:
        """從回覆中提取基本條款信息"""
        try:
            # 簡單的關鍵詞提取
            keywords = ["採購方式", "預算", "履約", "保固", "決標", "評選", "陸製品", "罰則"]
            found_clauses = []

            for keyword in keywords:
                if keyword in response:
                    found_clauses.append(keyword)

            return found_clauses if found_clauses else ["採購方式", "預算金額", "履約期限", "保固期間"]

        except Exception as e:
            logger.error(f"提取基本條款失敗: {e}")
            return ["採購方式", "預算金額", "履約期限", "保固期間"]
