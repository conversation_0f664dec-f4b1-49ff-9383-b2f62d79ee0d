"""
自訂 Prompt 管理 API
"""

from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.models.analysis_task import AnalysisTask
from app.services.analysis_task_service import AnalysisTaskService
import logging

logger = logging.getLogger(__name__)

router = APIRouter(tags=["prompt-management"])


class CustomPromptRequest(BaseModel):
    """自訂 Prompt 請求模型"""
    step_name: str = Field(..., description="步驟名稱 (clauses_analysis, compliance_check, final_report)")
    prompt_content: str = Field(..., description="Prompt 內容")
    description: Optional[str] = Field(None, description="Prompt 描述")


class TaskPromptConfigRequest(BaseModel):
    """任務 Prompt 配置請求模型"""
    task_id: str = Field(..., description="任務ID")
    custom_prompts: Dict[str, str] = Field(..., description="自訂 Prompt 字典")
    force_json_format: Optional[bool] = Field(None, description="是否強制 JSON 格式")


class PromptTemplateResponse(BaseModel):
    """Prompt 模板回應模型"""
    step_name: str
    default_prompt: str
    description: str
    variables: List[str]


@router.get("/templates", response_model=List[PromptTemplateResponse])
async def get_prompt_templates():
    """獲取可用的 Prompt 模板"""
    try:
        templates = [
            {
                "step_name": "clauses_analysis",
                "default_prompt": """
購案內容：
{content}

---
你是購案法規審查專家及助理，以上為送審的採購計畫，請協助逐項比較上述採購計畫中是否涵蓋下列審查要項類同之文字，並依序處理下列事項:
1.本案購案編號為何?
2.請以下列表格呈現各審查要項內容、採購計畫內容及涵蓋狀況，每欄避免過寬請自動換行並合宜排版

審查要項如下:
審查要項(決標原則-最低標):本案採分項報價、總價決標；各分項報價金額不可超過公告預算，否則判定不合格。
審查要項(決標原則-最有利標):依政府採購法第52條第1項第3款規定，經本案評選委員會就各評選項目評定序位第一，且經出席評選委員過半數之決定者為最有利標為得標廠商
審查要項(陸製品限制):本案採購計畫清單品項(含保固)產品及其製造(作)所需原料及元件，不同意開放廠商或其分包廠商，以原產地或廠牌為大陸地區、香港、澳門或其人(居)民、法人、團體、其他機構於第三地區投資之法人、機構或團體之財物。

請以 JSON 格式回應，包含：
- key_clauses: 關鍵條款列表
- potential_issues: 潛在問題
- compliance_areas: 需要檢查的法規領域
                """.strip(),
                "description": "購案條款分析 Prompt",
                "variables": ["content"]
            },
            {
                "step_name": "compliance_check",
                "default_prompt": """
基於以下條款分析結果，請進行詳細的法規比對檢查：

關鍵條款：{key_clauses}
需檢查的法規領域：{compliance_areas}
潛在問題：{potential_issues}

請檢查以下法規符合性：
1. 政府採購法相關規定
2. 國防部採購作業規定
3. 預算法相關條文
4. 契約條款標準範本
5. 驗收作業規定

請以 JSON 格式回應，包含：
- compliance_score: 符合度分數 (0-100)
- violations: 違規項目列表
- warnings: 警告項目列表
- recommendations: 改善建議
- detailed_checks: 詳細檢查結果
                """.strip(),
                "description": "法規比對檢查 Prompt",
                "variables": ["key_clauses", "compliance_areas", "potential_issues"]
            },
            {
                "step_name": "final_report",
                "default_prompt": """
基於以下法規比對檢查結果，請生成一份完整的法規比對報告：

符合度分數：{compliance_score}
違規項目：{violations}
警告項目：{warnings}
改善建議：{recommendations}

請生成一份專業的法規比對報告，包含：
1. 執行摘要
2. 主要發現
3. 風險評估
4. 改善建議
5. 後續行動計畫

請以 JSON 格式回應，包含：
- executive_summary: 執行摘要
- key_findings: 主要發現
- risk_assessment: 風險評估
- improvement_plan: 改善計畫
- compliance_score: 最終符合度分數
- violations: 違規項目
- recommendations: 建議事項
                """.strip(),
                "description": "最終報告生成 Prompt",
                "variables": ["compliance_score", "violations", "warnings", "recommendations"]
            }
        ]
        
        return templates
        
    except Exception as e:
        logger.error(f"獲取 Prompt 模板失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"獲取 Prompt 模板失敗: {str(e)}"
        )


@router.post("/tasks/{task_id}/configure")
async def configure_task_prompts(
    task_id: str,
    config: TaskPromptConfigRequest,
    db: Session = Depends(get_db)
):
    """為特定任務配置自訂 Prompt"""
    try:
        task_service = AnalysisTaskService(db)
        
        # 檢查任務是否存在
        task = task_service.get_task(task_id)
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"任務 {task_id} 不存在"
            )
        
        # 驗證 step_name
        valid_steps = ["clauses_analysis", "compliance_check", "final_report"]
        for step_name in config.custom_prompts.keys():
            if step_name not in valid_steps:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"無效的步驟名稱: {step_name}。有效的步驟: {valid_steps}"
                )
        
        # 更新任務配置
        current_config = task.config or {}
        current_config["custom_prompts"] = config.custom_prompts
        
        if config.force_json_format is not None:
            current_config["force_json_format"] = config.force_json_format
        
        # 保存配置
        task_service.update_task_config(task_id, current_config)
        
        logger.info(f"任務 {task_id} 的自訂 Prompt 配置已更新")
        
        return {
            "message": "自訂 Prompt 配置成功",
            "task_id": task_id,
            "configured_steps": list(config.custom_prompts.keys()),
            "force_json_format": config.force_json_format
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"配置任務 Prompt 失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"配置任務 Prompt 失敗: {str(e)}"
        )


@router.get("/tasks/{task_id}/prompts")
async def get_task_prompts(
    task_id: str,
    db: Session = Depends(get_db)
):
    """獲取任務的 Prompt 配置"""
    try:
        task_service = AnalysisTaskService(db)
        
        # 檢查任務是否存在
        task = task_service.get_task(task_id)
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"任務 {task_id} 不存在"
            )
        
        config = task.config or {}
        custom_prompts = config.get("custom_prompts", {})
        force_json_format = config.get("force_json_format", True)
        
        return {
            "task_id": task_id,
            "custom_prompts": custom_prompts,
            "force_json_format": force_json_format,
            "has_custom_prompts": len(custom_prompts) > 0
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取任務 Prompt 配置失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"獲取任務 Prompt 配置失敗: {str(e)}"
        )


@router.delete("/tasks/{task_id}/prompts/{step_name}")
async def remove_task_prompt(
    task_id: str,
    step_name: str,
    db: Session = Depends(get_db)
):
    """移除任務的特定步驟自訂 Prompt"""
    try:
        task_service = AnalysisTaskService(db)
        
        # 檢查任務是否存在
        task = task_service.get_task(task_id)
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"任務 {task_id} 不存在"
            )
        
        # 更新配置
        config = task.config or {}
        custom_prompts = config.get("custom_prompts", {})
        
        if step_name in custom_prompts:
            del custom_prompts[step_name]
            config["custom_prompts"] = custom_prompts
            task_service.update_task_config(task_id, config)
            
            logger.info(f"已移除任務 {task_id} 步驟 {step_name} 的自訂 Prompt")
            
            return {
                "message": f"已移除步驟 {step_name} 的自訂 Prompt",
                "task_id": task_id,
                "step_name": step_name
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"任務 {task_id} 步驟 {step_name} 沒有自訂 Prompt"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"移除任務 Prompt 失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"移除任務 Prompt 失敗: {str(e)}"
        )


@router.post("/validate")
async def validate_prompt(prompt_request: CustomPromptRequest):
    """驗證 Prompt 格式"""
    try:
        # 基本驗證
        if not prompt_request.prompt_content.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Prompt 內容不能為空"
            )
        
        # 檢查是否包含必要的變數
        step_variables = {
            "clauses_analysis": ["content"],
            "compliance_check": ["key_clauses", "compliance_areas", "potential_issues"],
            "final_report": ["compliance_score", "violations", "warnings", "recommendations"]
        }
        
        required_vars = step_variables.get(prompt_request.step_name, [])
        missing_vars = []
        
        for var in required_vars:
            if f"{{{var}}}" not in prompt_request.prompt_content:
                missing_vars.append(var)
        
        validation_result = {
            "valid": len(missing_vars) == 0,
            "step_name": prompt_request.step_name,
            "required_variables": required_vars,
            "missing_variables": missing_vars,
            "prompt_length": len(prompt_request.prompt_content)
        }
        
        if missing_vars:
            validation_result["warning"] = f"缺少必要變數: {', '.join(missing_vars)}"
        
        return validation_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"驗證 Prompt 失敗: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"驗證 Prompt 失敗: {str(e)}"
        )
