# 自訂 Prompt 功能說明

## 概述

購案分析系統現在支援自訂 AI Prompt 功能，讓使用者可以：

1. **自訂 AI 分析的 Prompt 內容**
2. **選擇是否強制要求 JSON 格式回覆**
3. **儲存完整的 LLM 原始回覆**
4. **支援不同分析步驟的獨立 Prompt 設定**

## 主要功能

### 1. 自訂 Prompt 支援

系統支援三個分析步驟的自訂 Prompt：

- **條款分析** (`clauses_analysis`): 分析購案文件中的關鍵條款
- **法規檢查** (`compliance_check`): 執行法規比對檢查
- **最終報告** (`final_report`): 生成法規比對報告

### 2. 回覆格式控制

- **強制 JSON 格式** (`force_json_format: true`): 
  - 嘗試解析 AI 回覆為 JSON 格式
  - 解析失敗時提供基本結構並保留原始回覆
  
- **允許原始回覆** (`force_json_format: false`):
  - 直接儲存 AI 的完整原始回覆
  - 適合自訂 prompt 要求特定格式的情況

### 3. 原始回覆儲存

無論選擇哪種格式，系統都會儲存：
- `raw_response`: LLM 的完整原始回覆
- `analysis_timestamp`: 分析時間戳
- `prompt_type`: 使用的 prompt 類型（custom/default）
- `model_used`: 使用的 AI 模型
- `json_parsed`: 是否成功解析為 JSON

## 配置方式

### 1. 全域配置

在 `backend/app/core/config.py` 中設定：

```python
# AI 回覆格式設置
FORCE_JSON_FORMAT: bool = True  # 是否強制要求 JSON 格式回覆
STORE_RAW_RESPONSES: bool = True  # 是否儲存原始 AI 回覆

# 自訂 Prompt 設置
CUSTOM_PROMPTS: Dict[str, str] = {
    "clauses_analysis": "您的全域自訂條款分析 prompt...",
    "compliance_check": "您的全域自訂法規檢查 prompt...",
    "final_report": "您的全域自訂報告生成 prompt..."
}
```

### 2. 任務級別配置

通過 API 為特定任務設定自訂 prompt：

```python
# 任務配置範例
task_config = {
    "custom_prompts": {
        "clauses_analysis": """
        請分析以下購案內容：
        {content}
        
        請提供詳細的分析報告，包括：
        1. 主要條款摘要
        2. 潛在風險點
        3. 建議改善事項
        
        請以自然語言回覆，不需要 JSON 格式。
        """,
        "compliance_check": "您的自訂法規檢查 prompt...",
        "final_report": "您的自訂報告生成 prompt..."
    },
    "force_json_format": False  # 允許原始回覆
}
```

## API 端點

### 1. 獲取 Prompt 模板

```http
GET /api/v1/prompt-management/templates
```

回應：
```json
[
  {
    "step_name": "clauses_analysis",
    "default_prompt": "預設的條款分析 prompt...",
    "description": "購案條款分析 Prompt",
    "variables": ["content"]
  }
]
```

### 2. 配置任務 Prompt

```http
POST /api/v1/prompt-management/tasks/{task_id}/configure
```

請求體：
```json
{
  "task_id": "task_123",
  "custom_prompts": {
    "clauses_analysis": "自訂的條款分析 prompt...",
    "compliance_check": "自訂的法規檢查 prompt..."
  },
  "force_json_format": false
}
```

### 3. 獲取任務 Prompt 配置

```http
GET /api/v1/prompt-management/tasks/{task_id}/prompts
```

### 4. 驗證 Prompt 格式

```http
POST /api/v1/prompt-management/validate
```

請求體：
```json
{
  "step_name": "clauses_analysis",
  "prompt_content": "您的 prompt 內容...",
  "description": "Prompt 描述"
}
```

## 前端組件

### PromptCustomizer.vue

提供完整的 Prompt 自訂界面：

- **多標籤頁設計**: 分別設定不同步驟的 prompt
- **格式驗證**: 即時驗證 prompt 格式和必要變數
- **預設重置**: 一鍵重置為預設 prompt
- **批量操作**: 支援載入、儲存、清除所有設定

使用方式：
```vue
<template>
  <PromptCustomizer :task-id="currentTaskId" />
</template>

<script setup>
import PromptCustomizer from '@/components/PromptCustomizer.vue'
</script>
```

## 變數系統

每個分析步驟支援特定的變數：

### 條款分析 (clauses_analysis)
- `{content}`: 購案文件內容

### 法規檢查 (compliance_check)
- `{key_clauses}`: 關鍵條款列表
- `{compliance_areas}`: 需檢查的法規領域
- `{potential_issues}`: 潛在問題

### 最終報告 (final_report)
- `{compliance_score}`: 符合度分數
- `{violations}`: 違規項目
- `{warnings}`: 警告項目
- `{recommendations}`: 改善建議

## 使用範例

### 1. 設定自然語言回覆

```python
custom_prompt = """
請分析以下購案內容：
{content}

請以自然語言提供詳細的分析報告，包括：

## 主要發現
- 列出關鍵條款和重要資訊

## 風險評估
- 識別潛在的法規風險

## 建議事項
- 提供具體的改善建議

請使用繁體中文回覆，格式清晰易讀。
"""

# 配置任務
task.config = {
    "custom_prompts": {
        "clauses_analysis": custom_prompt
    },
    "force_json_format": False
}
```

### 2. 設定特定格式回覆

```python
structured_prompt = """
請分析購案內容：{content}

請以以下格式回覆：

**購案編號**: [從內容中提取]
**主要條款**: 
1. [條款1]
2. [條款2]

**風險評估**: [高/中/低]
**建議事項**: 
- [建議1]
- [建議2]
"""

task.config = {
    "custom_prompts": {
        "clauses_analysis": structured_prompt
    },
    "force_json_format": False
}
```

## 測試

執行測試腳本：

```bash
cd backend
python test_custom_prompt.py
```

測試內容包括：
- 自訂 prompt 功能
- 原始回覆儲存
- JSON 格式控制
- 預設/自訂 prompt 切換

## 注意事項

1. **變數完整性**: 確保自訂 prompt 包含所有必要的變數
2. **格式一致性**: 如果需要結構化輸出，建議在 prompt 中明確指定格式
3. **長度限制**: 注意 AI 模型的 token 限制
4. **測試驗證**: 使用驗證 API 確保 prompt 格式正確

## 未來擴展

- 支援更多分析步驟的自訂 prompt
- 提供 prompt 模板庫
- 支援條件式 prompt（根據文件類型選擇不同 prompt）
- 增加 prompt 效果評估功能
